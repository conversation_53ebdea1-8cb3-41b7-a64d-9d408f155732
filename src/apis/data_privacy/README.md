# Data & Privacy Module

This module implements comprehensive privacy protection for the Feisty application, ensuring all user data remains local, secure, and under complete user control.

## Features

### 🔒 Data Storage (data_store.py)
- **Local SQLite Database**: All data stored exclusively on user's device
- **Encryption Support**: Optional AES-256 encryption for sensitive data
- **Screenshot Management**: Save, retrieve, and redact screenshot images
- **Analysis Storage**: Store AI analysis results with encryption
- **Retention Policies**: Automatic data cleanup based on configurable policies
- **Audit Trail**: Complete logging of all data operations

### 🛡️ Privacy Manager (privacy_manager.py)
- **Sensitive Data Detection**: Automatic detection of PII and sensitive information
  - Social Security Numbers
  - Credit Card Numbers
  - Email Addresses
  - Phone Numbers
  - API Keys and Passwords
  - IP Addresses
  - Bank Account Numbers
- **Configurable Sensitivity**: LOW, MEDIUM, HIGH, and PARANOID levels
- **Smart Redaction**: Type-specific placeholders for redacted content
- **Custom Patterns**: Add your own regex patterns for detection
- **Privacy Reports**: Generate comprehensive privacy audit reports

## Usage

### Basic Example

```python
from data_privacy import DataStore, PrivacyManager, SensitivityLevel

# Initialize with encryption
store = DataStore("my_data.db", password="secure_password")
manager = PrivacyManager(SensitivityLevel.HIGH)

# Save a screenshot
screenshot_id = store.save_screenshot("screenshot.png", tags=["demo"])

# Detect sensitive data in text
text = "Contact <NAME_EMAIL> or ************"
matches = manager.detect_sensitive_data(text)

# Redact sensitive content
redacted = manager.redact_content(text, matches)
print(redacted)  # "Contact me at [EMAIL-REDACTED] or [PHONE-REDACTED]"

# Save analysis with privacy info
store.save_analysis(
    screenshot_id,
    "text_analysis",
    {"original": text, "redacted": redacted},
    sensitive_data_detected=len(matches) > 0
)
```

### Running the Demo

```bash
python -m data_privacy
```

This will run comprehensive demonstrations of all features.

### Running Tests

```bash
python -m pytest tests/test_data_privacy.py -v
```

## Security Features

1. **Encryption at Rest**: All sensitive data encrypted using Fernet (AES-256)
2. **Local Only**: No network connections or cloud storage
3. **Secure Deletion**: Data is securely overwritten when deleted
4. **Access Control**: Password-protected database access
5. **Audit Logging**: All operations logged for compliance

## Configuration

### Retention Policies

Default retention periods:
- Screenshots: 90 days
- Analysis Results: 180 days
- Audit Logs: 30 days

### Sensitivity Levels

- **LOW**: Only high-confidence matches (80%+)
- **MEDIUM**: Moderate confidence matches (60%+)
- **HIGH**: Lower confidence matches (40%+)
- **PARANOID**: All potential matches (20%+)

## API Reference

See the source code documentation for detailed API information:
- `DataStore`: Main storage interface
- `PrivacyManager`: Privacy detection and redaction
- `SensitiveDataType`: Enumeration of detectable data types
- `SensitivityLevel`: Detection sensitivity levels

## Dependencies

- `cryptography`: For AES-256 encryption
- `Pillow`: For image manipulation and redaction
- `sqlite3`: Built-in Python module for database

## License

Part of the Feisty application. See main project license.