"""
Data Store Module for Feisty Application
Handles local storage, encryption, and data management
"""

import base64
import io
import json
import os
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any

from cryptography.fernet import <PERSON>rnet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from PIL import Image, ImageDraw


class DataStore:
    """
    Manages local SQLite database with encryption for storing screenshots,
    analysis results, and user data with privacy protection.
    """

    def __init__(self, db_path: str = "data_privacy.db", password: str | None = None):
        """
        Initialize the DataStore with optional encryption.
        
        Args:
            db_path: Path to the SQLite database file
            password: Optional password for database encryption
        """
        # Handle in-memory databases specially
        if db_path == ":memory:":
            self.db_path = db_path
            self._is_memory_db = True
        else:
            self.db_path = Path(db_path)
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            self._is_memory_db = False
        
        self.password = password
        self.cipher_suite = None

        if password:
            self._setup_encryption(password)

        self._initialize_database()

    def _setup_encryption(self, password: str):
        """Setup encryption using the provided password."""
        # Derive a key from the password
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'stable_salt_for_feisty',  # In production, use a random salt stored separately
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        self.cipher_suite = Fernet(key)

    def _encrypt_data(self, data: bytes) -> bytes:
        """Encrypt data if encryption is enabled."""
        if self.cipher_suite:
            return self.cipher_suite.encrypt(data)
        return data

    def _decrypt_data(self, data: bytes) -> bytes:
        """Decrypt data if encryption is enabled."""
        if self.cipher_suite:
            return self.cipher_suite.decrypt(data)
        return data

    def _initialize_database(self):
        """Initialize the SQLite database with required tables."""
        # For in-memory databases, maintain a persistent connection
        if self._is_memory_db:
            self._persistent_conn = sqlite3.connect(self.db_path)
            conn = self._persistent_conn
        else:
            self._persistent_conn = None
            conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        # Create screenshots table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS screenshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                image_data BLOB NOT NULL,
                width INTEGER NOT NULL,
                height INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                redacted BOOLEAN DEFAULT FALSE,
                tags TEXT,
                metadata TEXT
            )
        ''')

        # Create analysis results table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                screenshot_id INTEGER,
                analysis_type TEXT NOT NULL,
                result_data TEXT NOT NULL,
                confidence REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                sensitive_data_detected BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (screenshot_id) REFERENCES screenshots (id)
            )
        ''')

        # Create data retention policies table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS retention_policies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                data_type TEXT NOT NULL UNIQUE,
                retention_days INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Create audit log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                data_type TEXT,
                data_id INTEGER,
                details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Insert default retention policies
        default_policies = [
            ('screenshots', 90),
            ('analysis_results', 180),
            ('audit_log', 30)
        ]

        for data_type, days in default_policies:
            cursor.execute('''
                INSERT OR IGNORE INTO retention_policies (data_type, retention_days)
                VALUES (?, ?)
            ''', (data_type, days))

        conn.commit()
        if not self._persistent_conn:
            conn.close()

    def save_screenshot(self, image_path: str, tags: list[str] = None,
                       metadata: dict[str, Any] = None) -> int:
        """
        Save a screenshot to the database.
        
        Args:
            image_path: Path to the screenshot image file
            tags: Optional list of tags for categorization
            metadata: Optional metadata dictionary
            
        Returns:
            The ID of the saved screenshot
        """
        # Load the image
        with Image.open(image_path) as img:
            # Convert to bytes
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format=img.format or 'PNG')
            img_data = img_byte_arr.getvalue()

            # Encrypt if enabled
            encrypted_data = self._encrypt_data(img_data)

            # Save to database
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO screenshots (filename, image_data, width, height, tags, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                os.path.basename(image_path),
                encrypted_data,
                img.width,
                img.height,
                json.dumps(tags) if tags else None,
                json.dumps(metadata) if metadata else None
            ))

            screenshot_id = cursor.lastrowid

            # Log the action
            self._log_action(conn, 'save_screenshot', 'screenshots', screenshot_id)

            conn.commit()
            conn.close()

            return screenshot_id

    def save_analysis(self, screenshot_id: int, analysis_type: str,
                     result_data: dict[str, Any], confidence: float = None,
                     sensitive_data_detected: bool = False) -> int:
        """
        Save analysis results for a screenshot.
        
        Args:
            screenshot_id: ID of the associated screenshot
            analysis_type: Type of analysis performed
            result_data: Analysis results as a dictionary
            confidence: Optional confidence score
            sensitive_data_detected: Whether sensitive data was found
            
        Returns:
            The ID of the saved analysis result
        """
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        # Encrypt the result data if encryption is enabled
        result_json = json.dumps(result_data)
        if self.cipher_suite:
            encrypted_result = self._encrypt_data(result_json.encode()).decode('utf-8')
        else:
            encrypted_result = result_json

        cursor.execute('''
            INSERT INTO analysis_results 
            (screenshot_id, analysis_type, result_data, confidence, sensitive_data_detected)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            screenshot_id,
            analysis_type,
            encrypted_result,
            confidence,
            sensitive_data_detected
        ))

        analysis_id = cursor.lastrowid

        # Log the action
        self._log_action(conn, 'save_analysis', 'analysis_results', analysis_id)

        conn.commit()
        conn.close()

        return analysis_id

    def get_history(self, data_type: str = 'screenshots',
                   limit: int = 100, offset: int = 0,
                   start_date: datetime = None, end_date: datetime = None) -> list[dict[str, Any]]:
        """
        Retrieve historical data with pagination and filtering.
        
        Args:
            data_type: Type of data to retrieve ('screenshots' or 'analysis_results')
            limit: Maximum number of records to return
            offset: Number of records to skip
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of data records
        """
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Build the query
        query = f"SELECT * FROM {data_type} WHERE 1=1"
        params = []

        if start_date:
            query += " AND created_at >= ?"
            params.append(start_date.isoformat())

        if end_date:
            query += " AND created_at <= ?"
            params.append(end_date.isoformat())

        query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Convert to dictionaries and decrypt if needed
        results = []
        for row in rows:
            record = dict(row)

            # Decrypt sensitive fields
            if data_type == 'screenshots' and 'image_data' in record:
                # Don't include raw image data in history listing
                record['image_data'] = None
                record['has_image'] = True
            elif data_type == 'analysis_results' and 'result_data' in record:
                try:
                    if self.cipher_suite:
                        decrypted = self._decrypt_data(record['result_data'].encode()).decode('utf-8')
                        record['result_data'] = json.loads(decrypted)
                    else:
                        record['result_data'] = json.loads(record['result_data'])
                except:
                    record['result_data'] = {'error': 'Failed to decrypt or parse data'}

            # Parse JSON fields
            for field in ['tags', 'metadata']:
                if field in record and record[field]:
                    try:
                        record[field] = json.loads(record[field])
                    except:
                        pass

            results.append(record)

        conn.close()
        return results

    def redact_sensitive_data(self, screenshot_id: int,
                            regions: list[tuple[int, int, int, int]]) -> bool:
        """
        Redact sensitive regions in a screenshot using black boxes.
        
        Args:
            screenshot_id: ID of the screenshot to redact
            regions: List of (x, y, width, height) tuples defining regions to redact
            
        Returns:
            True if successful, False otherwise
        """
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        try:
            # Retrieve the screenshot
            cursor.execute('SELECT image_data FROM screenshots WHERE id = ?', (screenshot_id,))
            row = cursor.fetchone()

            if not row:
                return False

            # Decrypt the image data
            img_data = self._decrypt_data(row[0])

            # Load the image
            img = Image.open(io.BytesIO(img_data))

            # Create a drawing context
            draw = ImageDraw.Draw(img)

            # Redact specified regions with black rectangles
            for x, y, width, height in regions:
                draw.rectangle([x, y, x + width, y + height], fill='black')

            # Save the redacted image back
            img_byte_arr = io.BytesIO()
            img.save(img_byte_arr, format='PNG')
            redacted_data = img_byte_arr.getvalue()

            # Encrypt and update
            encrypted_data = self._encrypt_data(redacted_data)

            cursor.execute('''
                UPDATE screenshots 
                SET image_data = ?, redacted = TRUE 
                WHERE id = ?
            ''', (encrypted_data, screenshot_id))

            # Log the action
            self._log_action(conn, 'redact_screenshot', 'screenshots', screenshot_id,
                           {'regions': regions})

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            conn.close()
            print(f"Error redacting screenshot: {e}")
            return False

    def get_screenshot_image(self, screenshot_id: int) -> Image.Image | None:
        """
        Retrieve a screenshot image by ID.
        
        Args:
            screenshot_id: ID of the screenshot
            
        Returns:
            PIL Image object or None if not found
        """
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        cursor.execute('SELECT image_data FROM screenshots WHERE id = ?', (screenshot_id,))
        row = cursor.fetchone()
        conn.close()

        if row:
            img_data = self._decrypt_data(row[0])
            return Image.open(io.BytesIO(img_data))

        return None

    def enforce_retention_policies(self) -> dict[str, int]:
        """
        Delete data that exceeds retention policies.
        
        Returns:
            Dictionary with counts of deleted records by type
        """
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        # Get retention policies
        cursor.execute('SELECT data_type, retention_days FROM retention_policies')
        policies = cursor.fetchall()

        deleted_counts = {}

        for data_type, retention_days in policies:
            cutoff_date = datetime.now() - timedelta(days=retention_days)

            # Map policy types to table names
            table_map = {
                'screenshots': 'screenshots',
                'analysis_results': 'analysis_results',
                'audit_log': 'audit_log'
            }

            if data_type in table_map:
                table_name = table_map[data_type]

                # Count records to be deleted
                cursor.execute(
                    f'SELECT COUNT(*) FROM {table_name} WHERE created_at < ?',
                    (cutoff_date.isoformat(),)
                )
                count = cursor.fetchone()[0]

                # Delete old records
                cursor.execute(
                    f'DELETE FROM {table_name} WHERE created_at < ?',
                    (cutoff_date.isoformat(),)
                )

                deleted_counts[data_type] = count

                # Log the action
                if count > 0:
                    self._log_action(conn, 'enforce_retention', table_name, None,
                                   {'deleted_count': count, 'cutoff_date': cutoff_date.isoformat()})

        conn.commit()
        conn.close()

        return deleted_counts

    def _log_action(self, conn: sqlite3.Connection, action: str,
                   data_type: str, data_id: int | None,
                   details: dict[str, Any] = None):
        """Log an action to the audit log."""
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO audit_log (action, data_type, data_id, details)
            VALUES (?, ?, ?, ?)
        ''', (
            action,
            data_type,
            data_id,
            json.dumps(details) if details else None
        ))

    def export_data(self, output_path: str, data_types: list[str] = None) -> bool:
        """
        Export data to a JSON file for portability.
        
        Args:
            output_path: Path for the output file
            data_types: List of data types to export (default: all)
            
        Returns:
            True if successful
        """
        if data_types is None:
            data_types = ['screenshots', 'analysis_results']

        export_data = {}

        for data_type in data_types:
            export_data[data_type] = self.get_history(data_type, limit=999999)

        # Write to file
        with open(output_path, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)

        return True

    def get_storage_stats(self) -> dict[str, Any]:
        """Get storage statistics for the database."""
        conn = sqlite3.connect(str(self.db_path))
        cursor = conn.cursor()

        stats = {
            'database_size': os.path.getsize(self.db_path),
            'table_counts': {}
        }

        # Get record counts for each table
        tables = ['screenshots', 'analysis_results', 'audit_log']
        for table in tables:
            cursor.execute(f'SELECT COUNT(*) FROM {table}')
            stats['table_counts'][table] = cursor.fetchone()[0]

        conn.close()
        return stats
