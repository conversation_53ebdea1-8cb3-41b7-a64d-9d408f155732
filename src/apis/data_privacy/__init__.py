"""
Data & Privacy Module for Feisty Application

This module provides comprehensive privacy protection mechanisms including:
- Local-only data storage with encryption
- Automatic sensitive data detection
- Intelligent redaction capabilities
- Configurable retention policies
- Privacy settings management
"""

from .data_store import DataStore
from .privacy_manager import PrivacyManager, SensitiveDataMatch, SensitiveDataType, SensitivityLevel

__version__ = "1.0.0"
__all__ = [
    "DataStore",
    "PrivacyManager",
    "SensitiveDataType",
    "SensitivityLevel",
    "SensitiveDataMatch"
]
