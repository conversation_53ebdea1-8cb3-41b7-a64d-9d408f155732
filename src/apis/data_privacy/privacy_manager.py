"""
Privacy Manager Module for Feisty Application
Handles sensitive data detection, redaction, and privacy controls
"""

import re
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any


class SensitiveDataType(Enum):
    """Types of sensitive data that can be detected."""
    SSN = 'ssn'
    CREDIT_CARD = 'credit_card'
    BANK_ACCOUNT = 'bank_account'
    API_KEY = 'api_key'
    PASSWORD = 'password'
    EMAIL = 'email'
    PHONE = 'phone'
    IP_ADDRESS = 'ip_address'
    CUSTOM = 'custom'


class SensitivityLevel(Enum):
    """Sensitivity levels for detection."""
    LOW = 'low'
    MEDIUM = 'medium'
    HIGH = 'high'
    PARANOID = 'paranoid'


@dataclass
class SensitiveDataMatch:
    """Represents a match of sensitive data."""
    type: SensitiveDataType
    pattern: str
    positions: list[tuple[int, int]]  # List of (start, end) positions
    confidence: float
    suggested_action: str  # 'redact', 'encrypt', or 'warn'

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'type': self.type.value,
            'pattern': self.pattern,
            'positions': self.positions,
            'confidence': self.confidence,
            'suggested_action': self.suggested_action
        }


class PrivacyManager:
    """
    Manages privacy-related operations including sensitive data detection,
    redaction, and privacy settings management.
    """

    def __init__(self, sensitivity_level: SensitivityLevel = SensitivityLevel.MEDIUM):
        """
        Initialize the Privacy Manager.
        
        Args:
            sensitivity_level: Initial sensitivity level for detection
        """
        self.sensitivity_level = sensitivity_level
        self.custom_patterns: dict[str, re.Pattern] = {}
        self._initialize_patterns()
        self.privacy_settings = self._default_privacy_settings()
        self.redaction_log: list[dict[str, Any]] = []

    def _initialize_patterns(self):
        """Initialize regex patterns for sensitive data detection."""
        self.patterns = {
            SensitiveDataType.SSN: [
                # US Social Security Number formats
                re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
                re.compile(r'\b\d{3}\s\d{2}\s\d{4}\b'),
                re.compile(r'\b\d{9}\b')
            ],
            SensitiveDataType.CREDIT_CARD: [
                # Common credit card formats (simplified)
                re.compile(r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b'),
                re.compile(r'\b\d{4}[\s-]?\d{6}[\s-]?\d{5}\b'),  # Amex
            ],
            SensitiveDataType.EMAIL: [
                re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
            ],
            SensitiveDataType.PHONE: [
                # US phone numbers
                re.compile(r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b'),
                re.compile(r'\b\(\d{3}\)\s?\d{3}[-.\s]?\d{4}\b'),
                re.compile(r'\b\+1\s?\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b')
            ],
            SensitiveDataType.API_KEY: [
                # Common API key patterns
                re.compile(r'\b[A-Za-z0-9]{32,}\b'),  # Generic long alphanumeric
                re.compile(r'api[_-]?key["\s:=]+["\'`]?([A-Za-z0-9_\-]{20,})["\'`]?', re.IGNORECASE),
                re.compile(r'secret["\s:=]+["\'`]?([A-Za-z0-9_\-]{20,})["\'`]?', re.IGNORECASE),
                re.compile(r'token["\s:=]+["\'`]?([A-Za-z0-9_\-]{20,})["\'`]?', re.IGNORECASE)
            ],
            SensitiveDataType.PASSWORD: [
                re.compile(r'password["\s:=]+["\'`]?([^\s"\'`]+)["\'`]?', re.IGNORECASE),
                re.compile(r'pwd["\s:=]+["\'`]?([^\s"\'`]+)["\'`]?', re.IGNORECASE),
                re.compile(r'pass["\s:=]+["\'`]?([^\s"\'`]+)["\'`]?', re.IGNORECASE)
            ],
            SensitiveDataType.IP_ADDRESS: [
                # IPv4
                re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
                # IPv6 (simplified)
                re.compile(r'\b(?:[A-Fa-f0-9]{1,4}:){7}[A-Fa-f0-9]{1,4}\b')
            ],
            SensitiveDataType.BANK_ACCOUNT: [
                # US routing numbers (9 digits starting with 0-3)
                re.compile(r'\b[0-3]\d{8}\b'),
                # IBAN (simplified)
                re.compile(r'\b[A-Z]{2}\d{2}[A-Z0-9]{4,30}\b')
            ]
        }

    def _default_privacy_settings(self) -> dict[str, Any]:
        """Return default privacy settings."""
        return {
            'auto_redaction': True,
            'sensitivity_level': self.sensitivity_level.value,
            'retention_defaults': {
                'chat_message': 90,
                'generated_content': 180,
                'system_log': 30,
                'user_preference': -1  # Indefinite
            },
            'encryption_enabled': True,
            'audit_logging': True,
            'redaction_placeholder': '[REDACTED]',
            'notification_on_detection': True
        }

    def detect_sensitive_data(self, content: str) -> list[SensitiveDataMatch]:
        """
        Detect sensitive data in the provided content.
        
        Args:
            content: Text content to analyze
            
        Returns:
            List of sensitive data matches
        """
        matches = []

        # Check built-in patterns
        for data_type, patterns in self.patterns.items():
            for pattern in patterns:
                for match in pattern.finditer(content):
                    confidence = self._calculate_confidence(data_type, match.group())

                    # Apply sensitivity level filtering
                    if self._should_report(confidence):
                        matches.append(SensitiveDataMatch(
                            type=data_type,
                            pattern=match.group(),
                            positions=[(match.start(), match.end())],
                            confidence=confidence,
                            suggested_action=self._suggest_action(data_type, confidence)
                        ))

        # Check custom patterns
        for pattern_name, pattern in self.custom_patterns.items():
            for match in pattern.finditer(content):
                matches.append(SensitiveDataMatch(
                    type=SensitiveDataType.CUSTOM,
                    pattern=match.group(),
                    positions=[(match.start(), match.end())],
                    confidence=0.8,
                    suggested_action='redact'
                ))

        # Merge overlapping matches
        return self._merge_overlapping_matches(matches)

    def _calculate_confidence(self, data_type: SensitiveDataType, matched_text: str) -> float:
        """Calculate confidence score for a match."""
        confidence = 0.5  # Base confidence

        # Adjust based on data type and pattern characteristics
        if data_type == SensitiveDataType.SSN:
            # Check for valid SSN format and range
            digits = re.sub(r'\D', '', matched_text)
            if len(digits) == 9:
                confidence = 0.9
                # SSNs don't start with 000, 666, or 900-999
                if digits.startswith('000') or digits.startswith('666') or \
                   (digits[:3] >= '900' and digits[:3] <= '999'):
                    confidence = 0.3

        elif data_type == SensitiveDataType.CREDIT_CARD:
            # Basic Luhn algorithm check would go here
            confidence = 0.7

        elif data_type == SensitiveDataType.EMAIL:
            # Email patterns are quite reliable
            confidence = 0.95

        elif data_type == SensitiveDataType.API_KEY:
            # Longer keys are more likely to be real
            if len(matched_text) > 40:
                confidence = 0.8
            else:
                confidence = 0.6

        return confidence

    def _should_report(self, confidence: float) -> bool:
        """Determine if a match should be reported based on sensitivity level."""
        thresholds = {
            SensitivityLevel.LOW: 0.8,
            SensitivityLevel.MEDIUM: 0.6,
            SensitivityLevel.HIGH: 0.4,
            SensitivityLevel.PARANOID: 0.2
        }
        return confidence >= thresholds[self.sensitivity_level]

    def _suggest_action(self, data_type: SensitiveDataType, confidence: float) -> str:
        """Suggest an action based on data type and confidence."""
        if confidence > 0.8:
            if data_type in [SensitiveDataType.SSN, SensitiveDataType.CREDIT_CARD,
                           SensitiveDataType.API_KEY, SensitiveDataType.PASSWORD]:
                return 'redact'
            else:
                return 'encrypt'
        elif confidence > 0.5:
            return 'warn'
        else:
            return 'warn'

    def _merge_overlapping_matches(self, matches: list[SensitiveDataMatch]) -> list[SensitiveDataMatch]:
        """Merge overlapping matches to avoid duplicate detections."""
        if not matches:
            return matches

        # Sort by start position
        sorted_matches = sorted(matches, key=lambda m: m.positions[0][0])
        merged = [sorted_matches[0]]

        for match in sorted_matches[1:]:
            last_match = merged[-1]
            last_end = last_match.positions[-1][1]
            current_start = match.positions[0][0]

            # If overlapping, merge with higher confidence match
            if current_start <= last_end:
                if match.confidence > last_match.confidence:
                    merged[-1] = match
            else:
                merged.append(match)

        return merged

    def redact_content(self, content: str, matches: list[SensitiveDataMatch]) -> str:
        """
        Redact sensitive data from content.
        
        Args:
            content: Original content
            matches: List of sensitive data matches to redact
            
        Returns:
            Redacted content
        """
        if not matches:
            return content

        # Sort matches by position (reverse order for replacement)
        sorted_matches = sorted(matches, key=lambda m: m.positions[0][0], reverse=True)

        redacted = content
        for match in sorted_matches:
            for start, end in match.positions:
                # Create type-specific placeholder
                placeholder = self._get_redaction_placeholder(match.type, end - start)
                redacted = redacted[:start] + placeholder + redacted[end:]

        # Log redaction
        self.redaction_log.append({
            'timestamp': datetime.now().isoformat(),
            'matches_count': len(matches),
            'types': list(set(m.type.value for m in matches))
        })

        return redacted

    def _get_redaction_placeholder(self, data_type: SensitiveDataType, length: int) -> str:
        """Get appropriate redaction placeholder for data type."""
        placeholders = {
            SensitiveDataType.SSN: '[SSN-REDACTED]',
            SensitiveDataType.CREDIT_CARD: '[CC-REDACTED]',
            SensitiveDataType.EMAIL: '[EMAIL-REDACTED]',
            SensitiveDataType.PHONE: '[PHONE-REDACTED]',
            SensitiveDataType.API_KEY: '[API-KEY-REDACTED]',
            SensitiveDataType.PASSWORD: '[PASSWORD-REDACTED]',
            SensitiveDataType.IP_ADDRESS: '[IP-REDACTED]',
            SensitiveDataType.BANK_ACCOUNT: '[BANK-ACCT-REDACTED]',
            SensitiveDataType.CUSTOM: '[CUSTOM-REDACTED]'
        }
        return placeholders.get(data_type, '[REDACTED]')

    def add_custom_pattern(self, pattern: str, category: str):
        """
        Add a custom pattern for sensitive data detection.
        
        Args:
            pattern: Regular expression pattern
            category: Category name for the pattern
        """
        try:
            compiled_pattern = re.compile(pattern)
            self.custom_patterns[category] = compiled_pattern
        except re.error as e:
            raise ValueError(f"Invalid regex pattern: {e}")

    def configure_sensitivity_level(self, level: SensitivityLevel):
        """Update the sensitivity level for detection."""
        self.sensitivity_level = level
        self.privacy_settings['sensitivity_level'] = level.value

    def get_privacy_settings(self) -> dict[str, Any]:
        """Get current privacy settings."""
        return self.privacy_settings.copy()

    def update_privacy_settings(self, settings: dict[str, Any]):
        """Update privacy settings."""
        self.privacy_settings.update(settings)

        # Update sensitivity level if changed
        if 'sensitivity_level' in settings:
            try:
                self.sensitivity_level = SensitivityLevel(settings['sensitivity_level'])
            except ValueError:
                pass

    def export_redaction_rules(self) -> dict[str, Any]:
        """Export redaction rules and custom patterns."""
        return {
            'sensitivity_level': self.sensitivity_level.value,
            'custom_patterns': {
                name: pattern.pattern for name, pattern in self.custom_patterns.items()
            },
            'privacy_settings': self.privacy_settings,
            'export_date': datetime.now().isoformat()
        }

    def import_redaction_rules(self, rules: dict[str, Any]):
        """Import redaction rules and custom patterns."""
        if 'sensitivity_level' in rules:
            try:
                self.sensitivity_level = SensitivityLevel(rules['sensitivity_level'])
            except ValueError:
                pass

        if 'custom_patterns' in rules:
            for name, pattern in rules['custom_patterns'].items():
                try:
                    self.add_custom_pattern(pattern, name)
                except ValueError:
                    pass

        if 'privacy_settings' in rules:
            self.privacy_settings.update(rules['privacy_settings'])

    def get_privacy_audit_log(self) -> list[dict[str, Any]]:
        """Get the privacy audit log."""
        return self.redaction_log.copy()

    def generate_privacy_report(self) -> dict[str, Any]:
        """Generate a comprehensive privacy report."""
        return {
            'report_date': datetime.now().isoformat(),
            'sensitivity_level': self.sensitivity_level.value,
            'custom_patterns_count': len(self.custom_patterns),
            'redaction_summary': {
                'total_redactions': len(self.redaction_log),
                'recent_redactions': self.redaction_log[-10:] if self.redaction_log else []
            },
            'privacy_settings': self.privacy_settings,
            'detection_patterns': {
                data_type.value: len(patterns)
                for data_type, patterns in self.patterns.items()
            }
        }

    def analyze_image_regions(self, text_regions: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        Analyze text regions from an image for sensitive data.
        
        Args:
            text_regions: List of text regions with content and bounding boxes
            
        Returns:
            List of regions that contain sensitive data
        """
        sensitive_regions = []

        for region in text_regions:
            text = region.get('text', '')
            bbox = region.get('bbox', [])  # [x, y, width, height]

            matches = self.detect_sensitive_data(text)
            if matches:
                sensitive_regions.append({
                    'bbox': bbox,
                    'matches': [m.to_dict() for m in matches],
                    'original_text': text,
                    'redacted_text': self.redact_content(text, matches)
                })

        return sensitive_regions
