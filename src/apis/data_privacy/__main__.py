"""
Data & Privacy Module Example Usage
Demonstrates the key features of the data privacy system
"""

import os
import sys
from pathlib import Path

from PIL import Image, ImageDraw

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from src.apis.data_privacy.data_store import DataStore
from src.apis.data_privacy.privacy_manager import PrivacyManager, SensitivityLevel


def create_sample_screenshot():
    """Create a sample screenshot with some text for testing."""
    # Create a simple image with text
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)

    # Add some sample text that includes sensitive data
    sample_text = [
        "User Dashboard",
        "Name: <PERSON>",
        "Email: <EMAIL>",
        "Phone: (*************",
        "SSN: ***********",
        "Credit Card: 4111-1111-1111-1111",
        "API Key: sk_test_4eC39HqLyjWDarjtT1zdp7dc",
        "Password: MySecretPass123!",
        "IP Address: *************"
    ]

    y_position = 50
    for line in sample_text:
        draw.text((50, y_position), line, fill='black')
        y_position += 40

    # Save the image
    img_path = "sample_screenshot.png"
    img.save(img_path)
    return img_path


def demonstrate_data_store():
    """Demonstrate DataStore functionality."""
    print("=== DataStore Demonstration ===\n")

    # Initialize with encryption
    store = DataStore("demo_privacy.db", password="demo_password_123")

    # Create and save a sample screenshot
    img_path = create_sample_screenshot()
    print(f"Created sample screenshot: {img_path}")

    # Save screenshot with tags and metadata
    screenshot_id = store.save_screenshot(
        img_path,
        tags=["demo", "sensitive"],
        metadata={
            "source": "demo_application",
            "contains_pii": True
        }
    )
    print(f"Saved screenshot with ID: {screenshot_id}")

    # Save analysis results
    analysis_id = store.save_analysis(
        screenshot_id,
        "text_extraction",
        {
            "extracted_text": "Sample text with sensitive data",
            "confidence_score": 0.95,
            "processing_time": 0.234
        },
        confidence=0.95,
        sensitive_data_detected=True
    )
    print(f"Saved analysis with ID: {analysis_id}")

    # Get history
    print("\nRetrieving screenshot history:")
    history = store.get_history("screenshots", limit=5)
    for record in history:
        print(f"  - ID: {record['id']}, Created: {record['created_at']}, "
              f"Tags: {record.get('tags', [])}")

    # Demonstrate redaction
    print("\nRedacting sensitive regions in screenshot...")
    # Define regions to redact (x, y, width, height)
    sensitive_regions = [
        (150, 170, 200, 30),  # SSN
        (220, 210, 250, 30),  # Credit Card
        (140, 250, 400, 30),  # API Key
        (180, 290, 250, 30),  # Password
    ]

    success = store.redact_sensitive_data(screenshot_id, sensitive_regions)
    print(f"Redaction successful: {success}")

    # Get storage statistics
    stats = store.get_storage_stats()
    print("\nStorage Statistics:")
    print(f"  Database size: {stats['database_size']} bytes")
    print(f"  Record counts: {stats['table_counts']}")

    # Clean up
    os.remove(img_path)

    return store


def demonstrate_privacy_manager():
    """Demonstrate PrivacyManager functionality."""
    print("\n\n=== PrivacyManager Demonstration ===\n")

    # Initialize with medium sensitivity
    manager = PrivacyManager(SensitivityLevel.MEDIUM)

    # Sample content with various sensitive data
    sample_content = """
    Welcome to our application!
    
    User Profile:
    - Email: <EMAIL>
    - Phone: +****************
    - SSN: ***********
    
    Payment Information:
    - Card Number: 5105-1051-0510-5100
    - Account: *********
    
    Developer Settings:
    - API_KEY: "sk_live_abcdef*********0*********0*********0"
    - Database Password: "SuperSecret123!"
    - Server IP: ********
    """

    # Detect sensitive data
    print("Detecting sensitive data...")
    matches = manager.detect_sensitive_data(sample_content)

    print(f"\nFound {len(matches)} sensitive data matches:")
    for match in matches:
        print(f"  - Type: {match.type.value}")
        print(f"    Pattern: {match.pattern}")
        print(f"    Confidence: {match.confidence:.2f}")
        print(f"    Suggested Action: {match.suggested_action}")

    # Redact content
    print("\nRedacting sensitive content...")
    redacted_content = manager.redact_content(sample_content, matches)
    print("Redacted content preview:")
    print(redacted_content[:500] + "...")

    # Add custom pattern
    print("\nAdding custom pattern for employee IDs...")
    manager.add_custom_pattern(r'EMP-\d{6}', 'employee_id')

    # Test with custom pattern
    test_content = "Employee ID: EMP-123456 has access to the system."
    custom_matches = manager.detect_sensitive_data(test_content)
    print(f"Custom pattern matches: {len(custom_matches)}")

    # Configure sensitivity level
    print("\nChanging sensitivity level to HIGH...")
    manager.configure_sensitivity_level(SensitivityLevel.HIGH)

    # Export rules
    rules = manager.export_redaction_rules()
    print("\nExported redaction rules:")
    print(f"  Sensitivity: {rules['sensitivity_level']}")
    print(f"  Custom patterns: {list(rules['custom_patterns'].keys())}")

    # Generate privacy report
    report = manager.generate_privacy_report()
    print("\nPrivacy Report:")
    print(f"  Detection patterns configured: {sum(report['detection_patterns'].values())}")
    print(f"  Total redactions performed: {report['redaction_summary']['total_redactions']}")

    return manager


def demonstrate_retention_policies():
    """Demonstrate data retention policy enforcement."""
    print("\n\n=== Retention Policy Demonstration ===\n")

    # Create a store with some old data
    store = DataStore("retention_demo.db")

    # Note: In a real scenario, we would have data with various ages
    # For demo purposes, we'll just show the enforcement mechanism

    print("Enforcing retention policies...")
    deleted_counts = store.enforce_retention_policies()

    print("Retention policy enforcement results:")
    for data_type, count in deleted_counts.items():
        print(f"  - {data_type}: {count} records deleted")

    # Clean up
    os.remove("retention_demo.db")


def demonstrate_integration():
    """Demonstrate integrated workflow of privacy detection and storage."""
    print("\n\n=== Integrated Workflow Demonstration ===\n")

    # Initialize components
    store = DataStore("integrated_demo.db", password="secure_password")
    manager = PrivacyManager(SensitivityLevel.HIGH)

    # Simulate screenshot with text regions
    text_regions = [
        {
            'text': 'Contact: <EMAIL>',
            'bbox': [100, 100, 200, 30]
        },
        {
            'text': 'SSN: ***********',
            'bbox': [100, 150, 150, 30]
        },
        {
            'text': 'Normal text without sensitive data',
            'bbox': [100, 200, 300, 30]
        }
    ]

    # Analyze regions for sensitive data
    print("Analyzing text regions for sensitive data...")
    sensitive_regions = manager.analyze_image_regions(text_regions)

    print(f"\nFound {len(sensitive_regions)} regions with sensitive data:")
    for region in sensitive_regions:
        print(f"  - Original: {region['original_text']}")
        print(f"    Redacted: {region['redacted_text']}")
        print(f"    Bounding box: {region['bbox']}")

    # Create a demo screenshot and redact sensitive areas
    img_path = create_sample_screenshot()
    screenshot_id = store.save_screenshot(img_path, tags=["integrated_demo"])

    # Extract bounding boxes for redaction
    redaction_regions = []
    for region in sensitive_regions:
        bbox = region['bbox']
        if len(bbox) == 4:
            redaction_regions.append(tuple(bbox))

    if redaction_regions:
        print(f"\nRedacting {len(redaction_regions)} sensitive regions...")
        store.redact_sensitive_data(screenshot_id, redaction_regions)

    # Save privacy analysis
    store.save_analysis(
        screenshot_id,
        "privacy_scan",
        {
            "sensitive_regions": len(sensitive_regions),
            "redacted": True,
            "sensitivity_level": manager.sensitivity_level.value
        },
        sensitive_data_detected=len(sensitive_regions) > 0
    )

    print("\nIntegrated workflow completed successfully!")

    # Clean up
    os.remove(img_path)
    os.remove("integrated_demo.db")


def main():
    """Run all demonstrations."""
    print("Data & Privacy Feature Demonstration")
    print("=" * 50)

    try:
        # Demonstrate each component
        demonstrate_data_store()
        demonstrate_privacy_manager()
        demonstrate_retention_policies()
        demonstrate_integration()

        print("\n\nAll demonstrations completed successfully!")
        print("\nKey Features Demonstrated:")
        print("✓ Encrypted local storage with SQLite")
        print("✓ Screenshot saving and retrieval")
        print("✓ Sensitive data detection with multiple patterns")
        print("✓ Content redaction with type-specific placeholders")
        print("✓ Image region redaction for privacy")
        print("✓ Custom pattern support")
        print("✓ Configurable sensitivity levels")
        print("✓ Retention policy enforcement")
        print("✓ Privacy reporting and audit logs")
        print("✓ Integrated privacy workflow")

    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Clean up any remaining demo files
        for file in ["demo_privacy.db", "retention_demo.db", "integrated_demo.db"]:
            if os.path.exists(file):
                os.remove(file)


if __name__ == "__main__":
    main()
