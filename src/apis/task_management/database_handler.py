"""
SQLite database handler for task persistence.
"""

import json
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

from shared import Task
from enum import Enum

class TaskStatus(Enum):
    """Task status enumeration."""
    PENDING = "pending"
    ACTIVE = "active" 
    COMPLETED = "completed"
    PAUSED = "paused"
    ABANDONED = "abandoned"


class DatabaseHandler:
    """Handles SQLite database operations for tasks."""

    def __init__(self, db_path: str):
        """Initialize database connection and create tables if needed."""
        self.db_path = db_path
        self._ensure_directory()
        
        # For in-memory databases, we need to maintain a persistent connection
        if db_path == ":memory:":
            self._persistent_conn = sqlite3.connect(":memory:")
            self._create_tables_with_connection(self._persistent_conn)
        else:
            self._persistent_conn = None
            self._create_tables()

    def _ensure_directory(self):
        """Ensure the database directory exists."""
        # Skip directory creation for in-memory databases
        if self.db_path == ":memory:":
            return
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)

    def _get_connection(self):
        """Get a database connection, using persistent connection for in-memory databases."""
        if self._persistent_conn:
            return self._persistent_conn
        return sqlite3.connect(self.db_path)

    def _create_tables(self):
        """Create necessary database tables."""
        conn = self._get_connection()
        try:
            self._create_tables_with_connection(conn)
        finally:
            if not self._persistent_conn:
                conn.close()

    def _create_tables_with_connection(self, conn):
        """Create necessary database tables using provided connection."""
        conn.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                task_id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                priority INTEGER DEFAULT 3,
                description TEXT DEFAULT '',
                tags TEXT DEFAULT '[]',
                status TEXT DEFAULT 'pending',
                created_at TEXT NOT NULL,
                started_at TEXT,
                completed_at TEXT
            )
        ''')

        # Create index for better query performance
        conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_status 
            ON tasks(status)
        ''')

        conn.execute('''
            CREATE INDEX IF NOT EXISTS idx_priority 
            ON tasks(priority)
        ''')

        conn.commit()

    def add_task(self, task: Task) -> Task:
        """Add a new task to the database."""
        conn = self._get_connection()
        try:
            cursor = conn.execute('''
                INSERT INTO tasks (
                    title, priority, description, tags, status,
                    created_at, started_at, completed_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                task.title,
                task.priority,
                task.description,
                json.dumps(task.tags),
                task.status.value if hasattr(task.status, 'value') else task.status,
                task.created_at.isoformat() if task.created_at else datetime.now().isoformat(),
                task.started_at.isoformat() if task.started_at else None,
                task.completed_at.isoformat() if task.completed_at else None
            ))

            task.id = str(cursor.lastrowid)  # Update the id field for shared.Task
            conn.commit()
            return task
        finally:
            if not self._persistent_conn:
                conn.close()

    def update_task(self, task: Task) -> bool:
        """Update an existing task in the database."""
        if task.task_id is None:
            return False

        conn = self._get_connection()
        try:
            conn.execute('''
                UPDATE tasks SET
                    title = ?,
                    priority = ?,
                    description = ?,
                    tags = ?,
                    status = ?,
                    started_at = ?,
                    completed_at = ?
                WHERE task_id = ?
            ''', (
                task.title,
                task.priority,
                task.description,
                json.dumps(task.tags),
                task.status.value if hasattr(task.status, 'value') else task.status,
                task.started_at.isoformat() if task.started_at else None,
                task.completed_at.isoformat() if task.completed_at else None,
                task.task_id
            ))

            conn.commit()
            return conn.total_changes > 0
        finally:
            if not self._persistent_conn:
                conn.close()

    def get_task(self, task_id: int) -> Task | None:
        """Get a single task by ID."""
        conn = self._get_connection()
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM tasks WHERE task_id = ?
            ''', (task_id,))

            row = cursor.fetchone()
            if row:
                return self._row_to_task(row)
            return None
        finally:
            if not self._persistent_conn:
                conn.close()

    def get_all_tasks(self) -> list[Task]:
        """Get all tasks from the database."""
        tasks = []

        conn = self._get_connection()
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM tasks ORDER BY created_at DESC
            ''')

            for row in cursor:
                tasks.append(self._row_to_task(row))

            return tasks
        finally:
            if not self._persistent_conn:
                conn.close()

    def get_tasks_by_status(self, status: TaskStatus) -> list[Task]:
        """Get tasks by status."""
        tasks = []

        conn = self._get_connection()
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM tasks WHERE status = ? ORDER BY priority DESC
            ''', (status.value,))

            for row in cursor:
                tasks.append(self._row_to_task(row))

            return tasks
        finally:
            if not self._persistent_conn:
                conn.close()

    def delete_task(self, task_id: int) -> bool:
        """Delete a task from the database."""
        conn = self._get_connection()
        try:
            conn.execute('DELETE FROM tasks WHERE task_id = ?', (task_id,))
            conn.commit()
            return conn.total_changes > 0
        finally:
            if not self._persistent_conn:
                conn.close()

    def _row_to_task(self, row: sqlite3.Row) -> Task:
        """Convert a database row to a Task object."""
        # Create task with required fields
        task = Task(
            id=str(row['task_id']),  # Convert to string for shared.Task
            title=row['title'],
            priority=row['priority'],
            description=row['description'],
            status=row['status'],  # shared.Task expects string, not enum
            tags=json.loads(row['tags'])
        )

        # Parse datetime fields
        if row['created_at']:
            task.created_at = datetime.fromisoformat(row['created_at'])
        if row['started_at']:
            task.started_at = datetime.fromisoformat(row['started_at'])
        if row['completed_at']:
            task.completed_at = datetime.fromisoformat(row['completed_at'])

        return task

    def backup_database(self, backup_dir: str = None) -> str:
        """Create a backup of the database."""
        if backup_dir is None:
            backup_dir = Path(self.db_path).parent / 'backups'

        backup_dir = Path(backup_dir)
        backup_dir.mkdir(parents=True, exist_ok=True)

        # Create backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = backup_dir / f'tasks_backup_{timestamp}.db'

        # Copy database file
        shutil.copy2(self.db_path, backup_path)

        return str(backup_path)

    def get_task_statistics(self) -> dict:
        """Get statistics about tasks in the database."""
        conn = self._get_connection()
        try:
            # Total tasks
            total = conn.execute('SELECT COUNT(*) FROM tasks').fetchone()[0]

            # Tasks by status
            status_counts = {}
            cursor = conn.execute('''
                SELECT status, COUNT(*) FROM tasks GROUP BY status
            ''')
            for status, count in cursor:
                status_counts[status] = count

            # Tasks by priority
            priority_counts = {}
            cursor = conn.execute('''
                SELECT priority, COUNT(*) FROM tasks GROUP BY priority
            ''')
            for priority, count in cursor:
                priority_counts[priority] = count

            # Average completion time for completed tasks
            cursor = conn.execute('''
                SELECT AVG(
                    julianday(completed_at) - julianday(started_at)
                ) * 24 * 60 as avg_minutes
                FROM tasks 
                WHERE status = 'completed' 
                AND started_at IS NOT NULL 
                AND completed_at IS NOT NULL
            ''')
            avg_completion_time = cursor.fetchone()[0]

            return {
                'total_tasks': total,
                'by_status': status_counts,
                'by_priority': priority_counts,
                'avg_completion_minutes': avg_completion_time
            }
        finally:
            if not self._persistent_conn:
                conn.close()
