"""
Unit tests for the Task Management module.
"""

import csv
import json
import os
import shutil

# Add project root to path for imports
import sys
import tempfile
import unittest
from datetime import datetime, timedelta
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))

from src.apis.task_management import <PERSON><PERSON><PERSON><PERSON>, Task, TaskManager, TaskStatus


class TestTask(unittest.TestCase):
    """Test cases for the Task class."""

    def test_task_creation(self):
        """Test basic task creation."""
        task = Task(
            title="Test Task",
            priority=4,
            description="A test task",
            tags=["test", "unit"]
        )

        self.assertEqual(task.title, "Test Task")
        self.assertEqual(task.priority, 4)
        self.assertEqual(task.description, "A test task")
        self.assertEqual(task.tags, ["test", "unit"])
        self.assertEqual(task.status, TaskStatus.PENDING)
        self.assertIsNotNone(task.created_at)
        self.assertIsNone(task.started_at)
        self.assertIsNone(task.completed_at)

    def test_task_to_dict(self):
        """Test task serialization to dictionary."""
        task = Task(
            title="Test Task",
            priority=3,
            tags=["test"]
        )
        task.task_id = 1

        task_dict = task.to_dict()

        self.assertEqual(task_dict['title'], "Test Task")
        self.assertEqual(task_dict['priority'], 3)
        self.assertEqual(task_dict['tags'], ["test"])
        self.assertEqual(task_dict['status'], "pending")
        self.assertEqual(task_dict['task_id'], 1)

    def test_task_from_dict(self):
        """Test task deserialization from dictionary."""
        data = {
            'title': 'Test Task',
            'priority': 5,
            'description': 'Test description',
            'tags': ['urgent', 'bug'],
            'status': 'active',
            'task_id': 42,
            'created_at': datetime.now().isoformat(),
            'started_at': datetime.now().isoformat()
        }

        task = Task.from_dict(data)

        self.assertEqual(task.title, 'Test Task')
        self.assertEqual(task.priority, 5)
        self.assertEqual(task.description, 'Test description')
        self.assertEqual(task.tags, ['urgent', 'bug'])
        self.assertEqual(task.status, TaskStatus.ACTIVE)
        self.assertEqual(task.task_id, 42)
        self.assertIsNotNone(task.created_at)
        self.assertIsNotNone(task.started_at)


class TestTaskManager(unittest.TestCase):
    """Test cases for the TaskManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.manager = TaskManager()

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_add_and_get_task(self):
        """Test adding and retrieving tasks."""
        task = Task(title="Test Task", priority=3)
        self.manager.add_task(task)

        tasks = self.manager.get_all_tasks()
        self.assertEqual(len(tasks), 1)
        self.assertEqual(tasks[0].title, "Test Task")

    def test_current_task_management(self):
        """Test current task functionality."""
        # Add tasks
        task1 = Task(title="Task 1")
        task2 = Task(title="Task 2")
        self.manager.add_task(task1)
        self.manager.add_task(task2)

        # Initially no current task
        self.assertIsNone(self.manager.get_current_task())

        # Set first task as active
        self.manager.update_task_status(0, TaskStatus.ACTIVE)
        current = self.manager.get_current_task()
        self.assertIsNotNone(current)
        self.assertEqual(current.title, "Task 1")
        self.assertEqual(current.status, TaskStatus.ACTIVE)

        # Switch to second task
        self.manager.update_task_status(1, TaskStatus.ACTIVE)
        current = self.manager.get_current_task()
        self.assertEqual(current.title, "Task 2")

        # First task should be paused
        self.assertEqual(self.manager.tasks[0].status, TaskStatus.PAUSED)

    def test_update_task_status(self):
        """Test task status updates."""
        task = Task(title="Test Task")
        self.manager.add_task(task)

        # Test activation
        self.manager.update_task_status(0, TaskStatus.ACTIVE)
        self.assertEqual(self.manager.tasks[0].status, TaskStatus.ACTIVE)
        self.assertIsNotNone(self.manager.tasks[0].started_at)

        # Test completion
        self.manager.update_task_status(0, TaskStatus.COMPLETED)
        self.assertEqual(self.manager.tasks[0].status, TaskStatus.COMPLETED)
        self.assertIsNotNone(self.manager.tasks[0].completed_at)
        self.assertIsNone(self.manager.get_current_task())

    def test_load_csv(self):
        """Test loading tasks from CSV file."""
        csv_path = os.path.join(self.temp_dir, "tasks.csv")

        with open(csv_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['title', 'priority', 'description', 'tags'])
            writer.writerow(['Task 1', '5', 'High priority task', 'urgent,bug'])
            writer.writerow(['Task 2', '2', 'Low priority task', 'feature'])

        tasks = self.manager.load_tasks(csv_path)

        self.assertEqual(len(tasks), 2)
        self.assertEqual(tasks[0].title, 'Task 1')
        self.assertEqual(tasks[0].priority, 5)
        self.assertEqual(tasks[0].tags, ['urgent', 'bug'])
        self.assertEqual(tasks[1].title, 'Task 2')
        self.assertEqual(tasks[1].priority, 2)

    def test_load_json(self):
        """Test loading tasks from JSON file."""
        json_path = os.path.join(self.temp_dir, "tasks.json")

        tasks_data = [
            {
                'title': 'JSON Task 1',
                'priority': 4,
                'description': 'First JSON task',
                'tags': ['json', 'test']
            },
            {
                'title': 'JSON Task 2',
                'priority': 3
            }
        ]

        with open(json_path, 'w') as f:
            json.dump(tasks_data, f)

        tasks = self.manager.load_tasks(json_path)

        self.assertEqual(len(tasks), 2)
        self.assertEqual(tasks[0].title, 'JSON Task 1')
        self.assertEqual(tasks[0].priority, 4)
        self.assertEqual(tasks[0].tags, ['json', 'test'])

    def test_load_txt(self):
        """Test loading tasks from plain text file."""
        txt_path = os.path.join(self.temp_dir, "tasks.txt")

        with open(txt_path, 'w') as f:
            f.write("5: Urgent task\n")
            f.write("3: Normal task\n")
            f.write("Simple task without priority\n")
            f.write("\n")  # Empty line should be ignored
            f.write("1: Low priority task\n")

        tasks = self.manager.load_tasks(txt_path)

        self.assertEqual(len(tasks), 4)
        self.assertEqual(tasks[0].title, 'Urgent task')
        self.assertEqual(tasks[0].priority, 5)
        self.assertEqual(tasks[1].title, 'Normal task')
        self.assertEqual(tasks[1].priority, 3)
        self.assertEqual(tasks[2].title, 'Simple task without priority')
        self.assertEqual(tasks[2].priority, 3)  # Default priority

    def test_search_tasks(self):
        """Test task search functionality."""
        tasks = [
            Task(title="Fix login bug", description="Users cannot login", tags=["bug"]),
            Task(title="Add feature", description="New dashboard", tags=["feature"]),
            Task(title="Debug API", description="Fix bug in API", tags=["api", "bug"])
        ]

        for task in tasks:
            self.manager.add_task(task)

        # Search in title
        results = self.manager.search_tasks("Fix")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].title, "Fix login bug")

        # Search in description
        results = self.manager.search_tasks("dashboard")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].title, "Add feature")

        # Search in tags
        results = self.manager.search_tasks("bug")
        self.assertEqual(len(results), 2)

        # Case insensitive search
        results = self.manager.search_tasks("API")
        self.assertEqual(len(results), 1)

    def test_filter_by_status(self):
        """Test filtering tasks by status."""
        # Create tasks with different statuses
        for i, status in enumerate([TaskStatus.PENDING, TaskStatus.ACTIVE,
                                   TaskStatus.COMPLETED, TaskStatus.PENDING]):
            task = Task(title=f"Task {i}")
            self.manager.add_task(task)
            if status != TaskStatus.PENDING:
                self.manager.update_task_status(i, status)

        pending_tasks = self.manager.get_tasks_by_status(TaskStatus.PENDING)
        self.assertEqual(len(pending_tasks), 2)

        active_tasks = self.manager.get_tasks_by_status(TaskStatus.ACTIVE)
        self.assertEqual(len(active_tasks), 1)

        completed_tasks = self.manager.get_tasks_by_status(TaskStatus.COMPLETED)
        self.assertEqual(len(completed_tasks), 1)

    def test_filter_by_priority(self):
        """Test filtering tasks by priority."""
        priorities = [1, 3, 5, 3, 5]
        for i, priority in enumerate(priorities):
            task = Task(title=f"Task {i}", priority=priority)
            self.manager.add_task(task)

        high_priority = self.manager.get_tasks_by_priority(5)
        self.assertEqual(len(high_priority), 2)

        medium_priority = self.manager.get_tasks_by_priority(3)
        self.assertEqual(len(medium_priority), 2)

        low_priority = self.manager.get_tasks_by_priority(1)
        self.assertEqual(len(low_priority), 1)


class TestDatabaseHandler(unittest.TestCase):
    """Test cases for the DatabaseHandler class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.db_handler = DatabaseHandler(self.db_path)

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_add_and_get_task(self):
        """Test adding and retrieving tasks from database."""
        task = Task(
            title="DB Test Task",
            priority=4,
            description="Testing database",
            tags=["test", "db"]
        )

        # Add task
        saved_task = self.db_handler.add_task(task)
        self.assertIsNotNone(saved_task.task_id)

        # Get task
        retrieved_task = self.db_handler.get_task(saved_task.task_id)
        self.assertIsNotNone(retrieved_task)
        self.assertEqual(retrieved_task.title, "DB Test Task")
        self.assertEqual(retrieved_task.priority, 4)
        self.assertEqual(retrieved_task.tags, ["test", "db"])

    def test_update_task(self):
        """Test updating tasks in database."""
        task = Task(title="Original Title")
        saved_task = self.db_handler.add_task(task)

        # Update task
        saved_task.title = "Updated Title"
        saved_task.status = TaskStatus.COMPLETED
        saved_task.completed_at = datetime.now()

        success = self.db_handler.update_task(saved_task)
        self.assertTrue(success)

        # Verify update
        updated_task = self.db_handler.get_task(saved_task.task_id)
        self.assertEqual(updated_task.title, "Updated Title")
        self.assertEqual(updated_task.status, TaskStatus.COMPLETED)
        self.assertIsNotNone(updated_task.completed_at)

    def test_delete_task(self):
        """Test deleting tasks from database."""
        task = Task(title="To Be Deleted")
        saved_task = self.db_handler.add_task(task)

        # Delete task
        success = self.db_handler.delete_task(saved_task.task_id)
        self.assertTrue(success)

        # Verify deletion
        deleted_task = self.db_handler.get_task(saved_task.task_id)
        self.assertIsNone(deleted_task)

    def test_get_tasks_by_status(self):
        """Test retrieving tasks by status from database."""
        # Add tasks with different statuses
        statuses = [TaskStatus.PENDING, TaskStatus.ACTIVE, TaskStatus.COMPLETED, TaskStatus.PENDING]
        for i, status in enumerate(statuses):
            task = Task(title=f"Task {i}", status=status)
            if status == TaskStatus.COMPLETED:
                task.completed_at = datetime.now()
            self.db_handler.add_task(task)

        # Get by status
        pending = self.db_handler.get_tasks_by_status(TaskStatus.PENDING)
        self.assertEqual(len(pending), 2)

        completed = self.db_handler.get_tasks_by_status(TaskStatus.COMPLETED)
        self.assertEqual(len(completed), 1)

    def test_database_backup(self):
        """Test database backup functionality."""
        # Add some tasks
        for i in range(3):
            task = Task(title=f"Task {i}")
            self.db_handler.add_task(task)

        # Create backup
        backup_path = self.db_handler.backup_database()
        self.assertTrue(os.path.exists(backup_path))

        # Verify backup contains data
        backup_db = DatabaseHandler(backup_path)
        tasks = backup_db.get_all_tasks()
        self.assertEqual(len(tasks), 3)

    def test_task_statistics(self):
        """Test task statistics calculation."""
        # Add tasks with various properties
        now = datetime.now()

        # Completed task
        task1 = Task(title="Completed", status=TaskStatus.COMPLETED, priority=5)
        task1.started_at = now - timedelta(hours=2)
        task1.completed_at = now
        self.db_handler.add_task(task1)

        # Active task
        task2 = Task(title="Active", status=TaskStatus.ACTIVE, priority=3)
        self.db_handler.add_task(task2)

        # Pending tasks
        for i in range(2):
            task = Task(title=f"Pending {i}", priority=i+1)
            self.db_handler.add_task(task)

        stats = self.db_handler.get_task_statistics()

        self.assertEqual(stats['total_tasks'], 4)
        self.assertEqual(stats['by_status']['completed'], 1)
        self.assertEqual(stats['by_status']['active'], 1)
        self.assertEqual(stats['by_status']['pending'], 2)
        self.assertIsNotNone(stats['avg_completion_minutes'])


class TestIntegration(unittest.TestCase):
    """Integration tests for TaskManager with DatabaseHandler."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.manager = TaskManager(db_path=self.db_path)

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_persistence_across_instances(self):
        """Test that tasks persist across TaskManager instances."""
        # Add tasks in first instance
        task1 = Task(title="Persistent Task 1")
        task2 = Task(title="Persistent Task 2")
        self.manager.add_task(task1)
        self.manager.add_task(task2)
        self.manager.update_task_status(0, TaskStatus.ACTIVE)

        # Create new instance
        new_manager = TaskManager(db_path=self.db_path)

        # Verify tasks are loaded
        tasks = new_manager.get_all_tasks()
        self.assertEqual(len(tasks), 2)

        # Verify current task is maintained
        current = new_manager.get_current_task()
        self.assertIsNotNone(current)
        self.assertEqual(current.title, "Persistent Task 1")

    def test_file_import_with_persistence(self):
        """Test that imported tasks are persisted to database."""
        # Create test CSV
        csv_path = os.path.join(self.temp_dir, "import.csv")
        with open(csv_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['title', 'priority'])
            writer.writerow(['Imported Task 1', '4'])
            writer.writerow(['Imported Task 2', '5'])

        # Import tasks
        self.manager.load_tasks(csv_path)

        # Create new instance and verify
        new_manager = TaskManager(db_path=self.db_path)
        tasks = new_manager.get_all_tasks()
        self.assertEqual(len(tasks), 2)
        self.assertTrue(any(t.title == 'Imported Task 1' for t in tasks))
        self.assertTrue(any(t.title == 'Imported Task 2' for t in tasks))


if __name__ == '__main__':
    unittest.main()
