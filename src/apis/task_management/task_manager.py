"""
Task Management module for the Productivity Guard system.
Handles importing, tracking, and managing daily task lists.
"""

import csv
import json
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any
from shared import Task


class TaskStatus(Enum):
    """Enumeration for task status states."""
    PENDING = "pending"
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    ABANDONED = "abandoned"


class TaskManager:
    """Main class for managing tasks."""

    def __init__(self, db_path: str | None = None):
        """Initialize TaskManager with optional database path."""
        self.tasks: list[Task] = []
        self.current_task_index: int | None = None
        self.db_path = db_path

        # Initialize database handler if path provided
        if db_path:
            from .database_handler import DatabaseHandler
            self.db = DatabaseHandler(db_path)
            self._load_from_database()
        else:
            self.db = None

    def _load_from_database(self):
        """Load tasks from database."""
        if self.db:
            self.tasks = self.db.get_all_tasks()
            # Find the current active task
            for i, task in enumerate(self.tasks):
                if task.status == "active"  or (hasattr(task.status, 'value') and task.status.value == "active"):
                    self.current_task_index = i
                    break

    def get_current_task(self) -> Task | None:
        """Get the currently active task."""
        if self.current_task_index is not None and 0 <= self.current_task_index < len(self.tasks):
            return self.tasks[self.current_task_index]
        return None

    def load_tasks(self, file_path: str, file_format: str | None = None) -> list[Task]:
        """Load tasks from a file (CSV, JSON, or plain text)."""
        path = Path(file_path)

        if not path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Determine format from extension if not specified
        if file_format is None:
            ext = path.suffix.lower()
            if ext == '.csv':
                file_format = 'csv'
            elif ext == '.json':
                file_format = 'json'
            else:
                file_format = 'txt'

        # Load based on format
        if file_format == 'csv':
            loaded_tasks = self._load_csv(file_path)
        elif file_format == 'json':
            loaded_tasks = self._load_json(file_path)
        else:
            loaded_tasks = self._load_txt(file_path)

        # Add loaded tasks
        self.tasks.extend(loaded_tasks)

        # Save to database if available
        if self.db:
            for task in loaded_tasks:
                self.db.add_task(task)

        return loaded_tasks

    def _load_csv(self, file_path: str) -> list[Task]:
        """Load tasks from CSV file."""
        tasks = []
        with open(file_path, encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # Parse tags if present
                tags = []
                if 'tags' in row and row['tags']:
                    tags = [tag.strip() for tag in row['tags'].split(',')]

                task = Task(
                    id='',  # Will be assigned by database
                    title=row['title'],
                    priority=int(row.get('priority', 3)),
                    description=row.get('description', ''),
                    status='pending',
                    tags=tags
                )
                tasks.append(task)
        return tasks

    def _load_json(self, file_path: str) -> list[Task]:
        """Load tasks from JSON file."""
        tasks = []
        with open(file_path, encoding='utf-8') as f:
            data = json.load(f)

            # Handle both single task and list of tasks
            if isinstance(data, dict):
                data = [data]

            for item in data:
                task = Task(
                    id=item.get('id', ''),
                    title=item['title'],
                    priority=item.get('priority', 3),
                    description=item.get('description', ''),
                    status=item.get('status', 'pending'),
                    tags=item.get('tags', [])
                )
                # Set datetime fields if present
                if 'created_at' in item and item['created_at']:
                    task.created_at = datetime.fromisoformat(item['created_at'])
                if 'started_at' in item and item['started_at']:
                    task.started_at = datetime.fromisoformat(item['started_at'])
                if 'completed_at' in item and item['completed_at']:
                    task.completed_at = datetime.fromisoformat(item['completed_at'])
                tasks.append(task)

        return tasks

    def _load_txt(self, file_path: str) -> list[Task]:
        """Load tasks from plain text file (one task per line)."""
        tasks = []
        with open(file_path, encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    # Simple format: "priority: title"
                    if ':' in line and line[0].isdigit():
                        priority_str, title = line.split(':', 1)
                        priority = int(priority_str.strip())
                        title = title.strip()
                    else:
                        priority = 3
                        title = line

                    task = Task(id='', title=title, priority=priority, description='', status='pending')
                    tasks.append(task)

        return tasks

    def update_task_status(self, task_index: int, new_status: TaskStatus) -> bool:
        """Update the status of a task."""
        if 0 <= task_index < len(self.tasks):
            task = self.tasks[task_index]
            old_status = task.status

            # Convert status to string
            new_status_str = new_status.value if hasattr(new_status, 'value') else new_status
            old_status_str = old_status
            
            # Update timestamps based on status change
            if new_status_str == "active" and old_status_str != "active":
                # Deactivate current task if any
                if self.current_task_index is not None:
                    self.tasks[self.current_task_index].status = "paused"
                    if self.db:
                        self.db.update_task(self.tasks[self.current_task_index])

                # Activate new task
                task.started_at = datetime.now()
                self.current_task_index = task_index

            elif new_status_str == "completed":
                task.completed_at = datetime.now()
                if self.current_task_index == task_index:
                    self.current_task_index = None

            elif new_status_str == "abandoned":
                if self.current_task_index == task_index:
                    self.current_task_index = None

            # Update status
            task.status = new_status_str

            # Save to database if available
            if self.db:
                self.db.update_task(task)

            return True

        return False

    def get_all_tasks(self) -> list[Task]:
        """Get all tasks."""
        return self.tasks.copy()

    def add_task(self, task: Task) -> Task:
        """Add a new task."""
        self.tasks.append(task)

        # Save to database if available
        if self.db:
            task = self.db.add_task(task)
            
        # Check if this should be the current task
        if task.status == "active":
            # Find the task index
            for i, t in enumerate(self.tasks):
                if t.id == task.id:
                    self.current_task_index = i
                    break

        return task

    def get_task_by_id(self, task_id: int) -> Task | None:
        """Get a task by its ID."""
        for task in self.tasks:
            if hasattr(task, 'id') and task.id == str(task_id):
                return task
        return None

    def get_tasks_by_status(self, status: TaskStatus) -> list[Task]:
        """Get all tasks with a specific status."""
        status_str = status.value if hasattr(status, 'value') else status
        return [task for task in self.tasks if task.status == status_str]

    def get_tasks_by_priority(self, priority: int) -> list[Task]:
        """Get all tasks with a specific priority."""
        return [task for task in self.tasks if task.priority == priority]

    def search_tasks(self, query: str) -> list[Task]:
        """Search tasks by title or description."""
        query_lower = query.lower()
        results = []

        for task in self.tasks:
            if (query_lower in task.title.lower() or
                query_lower in task.description.lower() or
                any(query_lower in tag.lower() for tag in task.tags)):
                results.append(task)

        return results
