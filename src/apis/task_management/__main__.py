"""
Example usage of the Task Management module.
Run with: python -m task_management
"""

import os
from pathlib import Path

from .task_manager import Task, TaskManager, TaskStatus


def create_sample_files():
    """Create sample task files for demonstration."""
    samples_dir = Path("sample_tasks")
    samples_dir.mkdir(exist_ok=True)

    # Create CSV sample
    csv_content = """title,priority,description,tags
Complete project documentation,5,Write comprehensive docs for the new feature,"documentation,urgent"
Code review for PR #123,4,Review and provide feedback,"review,team"
Fix login bug,5,Users unable to login with special characters,"bug,urgent"
Update dependencies,2,Update all npm packages to latest versions,"maintenance"
Team meeting prep,3,Prepare slides for weekly team meeting,"meeting,planning"
"""

    with open(samples_dir / "tasks.csv", "w") as f:
        f.write(csv_content)

    # Create JSON sample
    json_content = """[
    {
        "title": "Implement user authentication",
        "priority": 5,
        "description": "Add JWT-based authentication system",
        "tags": ["backend", "security", "feature"]
    },
    {
        "title": "Design new dashboard UI",
        "priority": 4,
        "description": "Create mockups for the analytics dashboard",
        "tags": ["frontend", "design"]
    },
    {
        "title": "Database optimization",
        "priority": 3,
        "description": "Optimize slow queries identified in performance testing",
        "tags": ["database", "performance"]
    }
]"""

    with open(samples_dir / "tasks.json", "w") as f:
        f.write(json_content)

    # Create plain text sample
    txt_content = """5: Deploy to production
4: Write unit tests for new features
3: Refactor authentication module
2: Update README with new API endpoints
1: Clean up old commented code"""

    with open(samples_dir / "tasks.txt", "w") as f:
        f.write(txt_content)

    return samples_dir


def main():
    """Demonstrate Task Manager functionality."""
    print("Task Management System - Example Usage")
    print("=" * 50)

    # Create sample files
    samples_dir = create_sample_files()
    print(f"\nCreated sample task files in '{samples_dir}' directory")

    # Initialize TaskManager with database
    db_path = "tasks.db"
    manager = TaskManager(db_path=db_path)
    print(f"\nInitialized TaskManager with database: {db_path}")

    # Load tasks from different formats
    print("\n1. Loading tasks from different file formats:")

    # Load CSV
    csv_tasks = manager.load_tasks(samples_dir / "tasks.csv")
    print(f"   - Loaded {len(csv_tasks)} tasks from CSV")

    # Load JSON
    json_tasks = manager.load_tasks(samples_dir / "tasks.json")
    print(f"   - Loaded {len(json_tasks)} tasks from JSON")

    # Load plain text
    txt_tasks = manager.load_tasks(samples_dir / "tasks.txt")
    print(f"   - Loaded {len(txt_tasks)} tasks from plain text")

    # Display all tasks
    print(f"\n2. Total tasks loaded: {len(manager.get_all_tasks())}")
    print("\n   Task List:")
    for i, task in enumerate(manager.get_all_tasks()):
        print(f"   [{i}] Priority {task.priority}: {task.title}")
        if task.tags:
            print(f"       Tags: {', '.join(task.tags)}")

    # Set current task
    print("\n3. Task Status Management:")
    high_priority_tasks = manager.get_tasks_by_priority(5)
    if high_priority_tasks:
        # Find the index of the first high priority task
        for i, task in enumerate(manager.get_all_tasks()):
            if task.priority == 5 and task.status == TaskStatus.PENDING:
                print(f"   - Setting task [{i}] as active: {task.title}")
                manager.update_task_status(i, TaskStatus.ACTIVE)
                break

    # Get current task
    current = manager.get_current_task()
    if current:
        print(f"\n4. Current active task: {current.title}")
        print(f"   Status: {current.status.value}")
        print(f"   Started at: {current.started_at}")

    # Complete a task
    if current:
        all_tasks = manager.get_all_tasks()
        for i, task in enumerate(all_tasks):
            if task.task_id == current.task_id:
                print("\n5. Completing current task...")
                manager.update_task_status(i, TaskStatus.COMPLETED)
                print(f"   Task '{task.title}' marked as completed")
                break

    # Search tasks
    print("\n6. Task Search:")
    search_results = manager.search_tasks("bug")
    print(f"   - Found {len(search_results)} tasks containing 'bug':")
    for task in search_results:
        print(f"     * {task.title}")

    # Get tasks by status
    print("\n7. Tasks by Status:")
    for status in TaskStatus:
        tasks = manager.get_tasks_by_status(status)
        if tasks:
            print(f"   - {status.value.upper()}: {len(tasks)} tasks")

    # Database statistics
    if manager.db:
        stats = manager.db.get_task_statistics()
        print("\n8. Database Statistics:")
        print(f"   - Total tasks: {stats['total_tasks']}")
        print(f"   - By status: {stats['by_status']}")
        print(f"   - By priority: {stats['by_priority']}")
        if stats['avg_completion_minutes']:
            print(f"   - Average completion time: {stats['avg_completion_minutes']:.1f} minutes")

    # Create backup
    if manager.db:
        backup_path = manager.db.backup_database()
        print(f"\n9. Database backed up to: {backup_path}")

    # Add a new task programmatically
    print("\n10. Adding a new task programmatically:")
    new_task = Task(
        title="Implement task notifications",
        priority=4,
        description="Add email/push notifications for task reminders",
        tags=["feature", "notifications"]
    )
    added_task = manager.add_task(new_task)
    print(f"    - Added task with ID {added_task.task_id}: {added_task.title}")

    print("\n" + "=" * 50)
    print("Example completed successfully!")
    print(f"Tasks are stored in: {os.path.abspath(db_path)}")
    print(f"Sample files are in: {os.path.abspath(samples_dir)}")


if __name__ == "__main__":
    main()
