"""Configuration for screenshot capture functionality."""

from dataclasses import dataclass


@dataclass
class ScreenshotConfig:
    """Configuration settings for screenshot capture.
    
    Attributes:
        capture_interval: Time between captures in seconds (default: 1.0)
        output_directory: Directory to save screenshots (default: "./screenshots")
        image_format: Format for saving screenshots (default: "png")
        quality: Image quality for JPEG format (1-100, default: 95)
        monitor_index: Index of monitor to capture (None for all monitors, default: None)
        capture_region: Tuple of (x, y, width, height) to capture specific region (default: None)
        filename_prefix: Prefix for screenshot filenames (default: "screenshot")
        include_timestamp: Whether to include timestamp in filename (default: True)
    """
    capture_interval: float = 1.0
    output_directory: str = "./screenshots"
    image_format: str = "png"
    quality: int = 95
    monitor_index: int | None = None
    capture_region: tuple[int, int, int, int] | None = None
    filename_prefix: str = "screenshot"
    include_timestamp: bool = True
