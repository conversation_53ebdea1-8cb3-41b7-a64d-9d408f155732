"""Screenshot capture functionality using mss library."""

import os
import threading
from datetime import datetime
from pathlib import Path

import mss
from PIL import Image

from .config import ScreenshotConfig


class ScreenshotCapture:
    """Class for capturing screenshots with configurable options."""

    def __init__(self, config: ScreenshotConfig | None = None):
        """Initialize screenshot capture with configuration.
        
        Args:
            config: Configuration object for screenshot capture settings
        """
        self.config = config or ScreenshotConfig()
        self._capture_thread: threading.Thread | None = None
        self._stop_event = threading.Event()
        self._is_capturing = False

        # Create output directory if it doesn't exist
        Path(self.config.output_directory).mkdir(parents=True, exist_ok=True)

    def capture(self, filename: str | None = None) -> str:
        """Capture a single screenshot.
        
        Args:
            filename: Optional custom filename for the screenshot
            
        Returns:
            Path to the saved screenshot file
        """
        with mss.mss() as sct:
            # Determine what to capture
            if self.config.capture_region:
                # Capture specific region
                monitor = {
                    "top": self.config.capture_region[1],
                    "left": self.config.capture_region[0],
                    "width": self.config.capture_region[2],
                    "height": self.config.capture_region[3]
                }
            elif self.config.monitor_index is not None:
                # Capture specific monitor
                monitors = sct.monitors
                if 0 <= self.config.monitor_index < len(monitors):
                    monitor = monitors[self.config.monitor_index]
                else:
                    raise ValueError(f"Monitor index {self.config.monitor_index} out of range")
            else:
                # Capture all monitors (primary display)
                monitor = sct.monitors[0]

            # Capture the screenshot
            screenshot = sct.grab(monitor)

            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

            # Generate filename
            if filename is None:
                filename = self._generate_filename()

            filepath = os.path.join(self.config.output_directory, filename)

            # Save the image
            if self.config.image_format.lower() == "jpg" or self.config.image_format.lower() == "jpeg":
                img.save(filepath, "JPEG", quality=self.config.quality)
            else:
                img.save(filepath, self.config.image_format.upper())

            return filepath

    def start_capture_loop(self) -> None:
        """Start continuous screenshot capture in a separate thread."""
        if self._is_capturing:
            raise RuntimeError("Capture loop is already running")

        self._is_capturing = True
        self._stop_event.clear()
        self._capture_thread = threading.Thread(target=self._capture_loop)
        self._capture_thread.start()

    def stop_capture_loop(self) -> None:
        """Stop the continuous screenshot capture."""
        if not self._is_capturing:
            return

        self._stop_event.set()
        if self._capture_thread:
            self._capture_thread.join()
        self._is_capturing = False

    def is_capturing(self) -> bool:
        """Check if capture loop is currently running.
        
        Returns:
            True if capturing, False otherwise
        """
        return self._is_capturing

    def get_monitors(self) -> list[dict]:
        """Get list of available monitors.
        
        Returns:
            List of monitor dictionaries with their properties
        """
        with mss.mss() as sct:
            return sct.monitors

    def _capture_loop(self) -> None:
        """Internal method for continuous capture loop."""
        while not self._stop_event.is_set():
            try:
                self.capture()
            except Exception as e:
                print(f"Error capturing screenshot: {e}")

            # Wait for the specified interval
            self._stop_event.wait(self.config.capture_interval)

    def _generate_filename(self) -> str:
        """Generate a filename for the screenshot.
        
        Returns:
            Generated filename string
        """
        parts = [self.config.filename_prefix]

        if self.config.include_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            parts.append(timestamp)

        filename = "_".join(parts) + f".{self.config.image_format}"
        return filename
