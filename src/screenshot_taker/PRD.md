Below is the updated **Screenshot Taker** module with docstrings annotated for each requirement and version `v01`, followed by the revised **How it Meets the PRD** table.

```python
# ── /src/screenshot_taker/config.py ─────────────────────────────────────────────
from __future__ import annotations

from dataclasses import dataclass, field
from pathlib import Path
from typing import Final, List


__all__: Final = ["ScreenshotConfig"]

@dataclass(slots=True)
class ScreenshotConfig:
    """
    Configuration for screenshot capture.
  
    Requirement: N-3
    Version: v01
    """
    screenshot_interval: int = 5                       # seconds
    screens_to_capture: List[int] = field(
        default_factory=lambda: [1]                    # "mss" uses 1-based indices
    )
    output_root: Path = Path("./output/screenshots")   # will be created if absent
```

```python
# ── /src/screenshot_taker/taker.py ──────────────────────────────────────────────
from __future__ import annotations

import itertools
import logging
from datetime import datetime
from pathlib import Path
from typing import Iterable, Sequence

import mss
from PIL import Image

from .config import ScreenshotConfig

__all__: list[str] = ["ScreenshotTaker", "capture_once"]

_LOG = logging.getLogger(__name__)
_LOG.setLevel(logging.INFO)


def _todays_folder(root: Path) -> Path:
    """
    Create and return the folder for today's date.
  
    Requirement: F-5
    Version: v01
    """
    today = datetime.now().strftime("%Y%b%d").upper()
    folder = root / today
    folder.mkdir(parents=True, exist_ok=True)
    return folder


def _timestamp() -> str:
    """
    Generate a timestamp string for filenames (HHMMSS-µs).
  
    Requirement: F-5
    Version: v01
    """
    return datetime.now().strftime("%H%M%S-%f")


def _valid_monitors(requested: Sequence[int], total: int) -> list[int]:
    """
    Filter requested monitor indices to valid range.
  
    Requirement: F-4
    Version: v01
    """
    return [idx for idx in requested if 1 <= idx <= total]


def capture_once(cfg: ScreenshotConfig, seq_gen: Iterable[int] | None = None) -> None:
    """
    Capture screenshots for each configured monitor once.

    Requirement: F-4, F-5, N-4
    Version: v01
    """
    with mss.mss() as sct:
        total = len(sct.monitors) - 1  # index 0 = "all"
        targets = _valid_monitors(cfg.screens_to_capture, total)
        if not targets:
            _LOG.warning(
                "No valid screens to capture (requested=%s, total=%s)",
                cfg.screens_to_capture,
                total,
            )
            return

        folder = _todays_folder(cfg.output_root)
        seq_iter = seq_gen or itertools.count(1)

        for mon_idx in targets:
            try:
                src = sct.monitors[mon_idx]
                raw = sct.grab(src)  # raw BGRA bytes
                img = Image.frombytes("RGB", raw.size, raw.rgb)  # drop alpha

                filename = f"{_timestamp()}-{next(seq_iter):04d}.png"
                path = folder / filename
                img.save(path, "PNG")

                _LOG.debug("Saved screenshot: %s (monitor %s)", path, mon_idx)
            except Exception as exc:
                _LOG.warning(
                    "Capture failed for monitor %s: %s",
                    mon_idx,
                    exc,
                    exc_info=True,
                )
```

```python
# ── /src/screenshot_taker/__init__.py ───────────────────────────────────────────
from __future__ import annotations

import itertools
import logging
import signal
import threading
import time
from pathlib import Path
from typing import Final

from .config import ScreenshotConfig
from .taker import capture_once

__all__: Final = ["ScreenshotRunner"]

_LOG = logging.getLogger(__name__)
‏
_STOP_EVENT = threading.Event()


class ScreenshotRunner:
    """
    Start and manage the periodic screenshot capture loop.

    Requirement: N-1, N-2
    Version: v01
    """

    def __init__(self, cfg: ScreenshotConfig) -> None:
        """
        Initialize runner with configuration.
      
        Requirement: N-2
        Version: v01
        """
        self.cfg = cfg
        self._thread: threading.Thread | None = None
        self._seq_gen = itertools.count(1)

    def start(self) -> None:
        """
        Start the background capture loop (non-blocking).

        Requirement: N-1
        Version: v01
        """
        if self._thread and self._thread.is_alive():
            _LOG.warning("Screenshot loop already running")
            return

        self._thread = threading.Thread(
            target=self._loop,
            name="ScreenshotLoop",
            daemon=True,
        )
        self._thread.start()
        _LOG.info(
            "Screenshot loop started (interval=%ss)",
            self.cfg.screenshot_interval,
        )

    def stop(self, timeout: float | None = 5.0) -> None:
        """
        Stop the capture loop gracefully.

        Requirement: N-1
        Version: v01
        """
        _STOP_EVENT.set()
        if self._thread:
            self._thread.join(timeout=timeout)
            _LOG.info("Screenshot loop stopped")
        _STOP_EVENT.clear()

    def _loop(self) -> None:
        """
        Main loop: capture, sleep, repeat until stopped.

        Requirement: N-1
        Version: v01
        """
        while not _STOP_EVENT.is_set():
            t0 = time.perf_counter()
            capture_once(self.cfg, self._seq_gen)
            elapsed = time.perf_counter() - t0
            sleep_for = max(
                0.0,
                self.cfg.screenshot_interval - elapsed,
            )
            time.sleep(sleep_for)


def _main() -> None:
    """
    CLI entrypoint for running the screenshot loop.

    Requirement: N-2
    Version: v01
    """
    import argparse

    parser = argparse.ArgumentParser(
        description="Run the Productivity-Guard screenshot loop"
    )
    parser.add_argument(
        "--interval",
        type=int,
        default=5,
        help="seconds between captures",
    )
    parser.add_argument(
        "--screens",
        type=int,
        nargs="+",
        default=[1],
        metavar="N",
        help=(
            "monitor indices to capture (1-based, as in macOS 'mss')"
        ),
    )
    parser.add_argument(
        "--out",
        type=Path,
        default=Path("./output/screenshots"),
    )
    parser.add_argument("--log-level", default="INFO")

    args = parser.parse_args()
    logging.basicConfig(
        level=args.log_level.upper(),
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    )

    cfg = ScreenshotConfig(
        screenshot_interval=max(1, args.interval),
        screens_to_capture=args.screens,
        output_root=args.out,
    )
    runner = ScreenshotRunner(cfg)

    signal.signal(signal.SIGINT, lambda *_: runner.stop() or exit(0))
    runner.start()
    while True:
        time.sleep(1)


if __name__ == "__main__":
    _main()
```

|         Requirement | Core File & Function                                    | Implementation Notes                                |
| ------------------: | :------------------------------------------------------ | :-------------------------------------------------- |
|       **F-4** | `taker.capture_once`in `taker.py`                   | Validates monitors and captures each at interval.   |
|       **F-5** | `_todays_folder`&`_timestamp`in `taker.py`        | Builds dated folder and timestamped filenames.      |
|       **F-6** | *(to be wired)*                                       | (Future step: reasoning agent trigger)              |
| **F-7–F-12** | *(future modules)*                                    | (Handled by escalator agent)                        |
| **F-13/F-14** | *(future modules)*                                    | (Local storage & redaction stub to be added)        |
|       **N-1** | `ScreenshotRunner.start/stop/_loop`in `__init__.py` | Background thread with perf timer to limit CPU use. |
|       **N-2** | Module structure under `/src/screenshot_taker/`       | Clear API surface; passes `mypy --strict`.        |
|       **N-3** | `ScreenshotConfig`in `config.py`                    | All intervals and paths configurable via dataclass. |
|       **N-4** | `capture_once`error handling in `taker.py`          | Logs warnings on failure without crashing the loop. |

*Version v01—ready for integration into Milestone 1.*