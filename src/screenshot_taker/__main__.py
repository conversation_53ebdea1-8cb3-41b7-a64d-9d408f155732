"""Example usage of the screenshot capture module."""

import sys
import time
from pathlib import Path

from .capture import ScreenshotCapture
from .config import ScreenshotConfig


def main():
    """Demonstrate screenshot capture functionality."""
    print("Screenshot Capture Example")
    print("-" * 50)

    # Example 1: Single screenshot with default settings
    print("\n1. Capturing a single screenshot with default settings...")
    capture = ScreenshotCapture()
    filepath = capture.capture()
    print(f"   Screenshot saved to: {filepath}")

    # Example 2: Custom configuration
    print("\n2. Capturing with custom configuration...")
    config = ScreenshotConfig(
        output_directory="./custom_screenshots",
        image_format="jpg",
        quality=85,
        filename_prefix="custom",
        capture_interval=2.0
    )
    custom_capture = ScreenshotCapture(config)
    filepath = custom_capture.capture()
    print(f"   Screenshot saved to: {filepath}")

    # Example 3: List available monitors
    print("\n3. Available monitors:")
    monitors = capture.get_monitors()
    for i, monitor in enumerate(monitors):
        print(f"   Monitor {i}: {monitor}")

    # Example 4: Capture specific region
    print("\n4. Capturing a specific region (top-left 800x600)...")
    region_config = ScreenshotConfig(
        capture_region=(0, 0, 800, 600),
        filename_prefix="region"
    )
    region_capture = ScreenshotCapture(region_config)
    filepath = region_capture.capture()
    print(f"   Region screenshot saved to: {filepath}")

    # Example 5: Continuous capture loop
    print("\n5. Starting continuous capture (5 seconds)...")
    loop_config = ScreenshotConfig(
        capture_interval=1.0,
        filename_prefix="continuous"
    )
    loop_capture = ScreenshotCapture(loop_config)

    print("   Starting capture loop...")
    loop_capture.start_capture_loop()

    # Let it run for 5 seconds
    for i in range(5):
        print(f"   Capturing... {i+1}/5")
        time.sleep(1)

    print("   Stopping capture loop...")
    loop_capture.stop_capture_loop()
    print("   Capture loop stopped.")

    # Example 6: Error handling
    print("\n6. Error handling example...")
    try:
        error_config = ScreenshotConfig(monitor_index=99)  # Invalid monitor
        error_capture = ScreenshotCapture(error_config)
        error_capture.capture()
    except ValueError as e:
        print(f"   Expected error caught: {e}")

    print("\n" + "-" * 50)
    print("Examples completed!")

    # Clean up created directories (optional)
    if "--cleanup" in sys.argv:
        print("\nCleaning up created directories...")
        for dir_path in ["./screenshots", "./custom_screenshots"]:
            if Path(dir_path).exists():
                import shutil
                shutil.rmtree(dir_path)
                print(f"   Removed {dir_path}")


if __name__ == "__main__":
    main()
