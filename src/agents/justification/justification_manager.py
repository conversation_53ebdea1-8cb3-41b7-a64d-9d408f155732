"""Manager for handling user justifications when task switches are detected."""

import asyncio
import threading
import time
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional, Callable, Any
import sys

try:
    import tkinter as tk
    from tkinter import simpledialog, messagebox
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False

from shared.types import get_logger

logger = get_logger(__name__)


class JustificationStatus(Enum):
    """Status of justification request."""
    PENDING = "pending"
    ACCEPTED = "accepted"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


@dataclass
class JustificationResult:
    """Result of a justification request."""
    status: JustificationStatus
    justification_text: Optional[str]
    response_time_seconds: float
    timestamp: datetime


@dataclass  
class JustificationConfig:
    """Configuration for justification system."""
    timeout_seconds: int = 60  # F-9: 60 second timeout
    use_gui: bool = True  # Use GUI prompts vs terminal
    notification_sound: bool = True
    persistent_prompt: bool = True  # Keep prompting until answered
    prompt_message: str = "It looks like you've switched tasks. Why?"  # F-7


class JustificationManager:
    """Manages user justification prompts and responses."""
    
    def __init__(self, config: Optional[JustificationConfig] = None):
        """Initialize justification manager."""
        self.config = config or JustificationConfig()
        self._active_prompt: Optional[threading.Thread] = None
        self._prompt_result: Optional[JustificationResult] = None
        self._prompt_lock = threading.Lock()
        self._cancelled = False
        
    def prompt_for_justification(
        self, 
        task_name: str,
        callback: Optional[Callable[[JustificationResult], None]] = None
    ) -> JustificationResult:
        """
        Prompt user for justification of task switch.
        
        Args:
            task_name: Name of the task user should be working on
            callback: Optional callback when justification is received
            
        Returns:
            JustificationResult with status and text
        """
        logger.info(f"Prompting for justification - expected task: {task_name}")
        
        with self._prompt_lock:
            # Cancel any existing prompt
            if self._active_prompt and self._active_prompt.is_alive():
                self._cancelled = True
                self._active_prompt.join(timeout=1)
            
            self._cancelled = False
            self._prompt_result = None
            
            # Start prompt in background thread
            self._active_prompt = threading.Thread(
                target=self._prompt_worker,
                args=(task_name, callback),
                daemon=True
            )
            self._active_prompt.start()
            
            # Wait for result or timeout
            self._active_prompt.join(timeout=self.config.timeout_seconds)
            
            if self._active_prompt.is_alive():
                # Timeout occurred
                self._cancelled = True
                logger.warning(f"Justification prompt timed out after {self.config.timeout_seconds}s")
                result = JustificationResult(
                    status=JustificationStatus.TIMEOUT,
                    justification_text=None,
                    response_time_seconds=self.config.timeout_seconds,
                    timestamp=datetime.now()
                )
            else:
                # Got result
                result = self._prompt_result or JustificationResult(
                    status=JustificationStatus.CANCELLED,
                    justification_text=None,
                    response_time_seconds=0,
                    timestamp=datetime.now()
                )
            
            if callback:
                callback(result)
                
            return result
    
    def _prompt_worker(self, task_name: str, callback: Optional[Callable]) -> None:
        """Worker thread for prompting user."""
        start_time = time.time()
        
        try:
            if self.config.use_gui:
                justification = self._prompt_gui(task_name)
            else:
                justification = self._prompt_terminal(task_name)
            
            if self._cancelled:
                return
                
            response_time = time.time() - start_time
            
            if justification is not None:
                self._prompt_result = JustificationResult(
                    status=JustificationStatus.ACCEPTED,
                    justification_text=justification,
                    response_time_seconds=response_time,
                    timestamp=datetime.now()
                )
                logger.info(f"Received justification: {justification[:50]}...")
            else:
                self._prompt_result = JustificationResult(
                    status=JustificationStatus.CANCELLED,
                    justification_text=None,
                    response_time_seconds=response_time,
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            logger.error(f"Error in justification prompt: {e}")
            self._prompt_result = JustificationResult(
                status=JustificationStatus.CANCELLED,
                justification_text=None,
                response_time_seconds=time.time() - start_time,
                timestamp=datetime.now()
            )
    
    def _prompt_gui(self, task_name: str) -> Optional[str]:
        """Show GUI prompt for justification."""
        if not TKINTER_AVAILABLE:
            logger.warning("tkinter not available, falling back to terminal prompt")
            return self._prompt_terminal(task_name)
            
        try:
            # Create root window
            root = tk.Tk()
            root.withdraw()  # Hide main window
            
            # Position at center of screen
            root.update_idletasks()
            x = (root.winfo_screenwidth() // 2) - 200
            y = (root.winfo_screenheight() // 2) - 100
            root.geometry(f"+{x}+{y}")
            
            # Play notification sound if enabled
            if self.config.notification_sound:
                root.bell()
            
            # Show dialog
            message = f"{self.config.prompt_message}\n\nYou should be working on: {task_name}"
            justification = simpledialog.askstring(
                "Task Switch Detected",
                message,
                parent=root
            )
            
            root.destroy()
            return justification
            
        except Exception as e:
            logger.error(f"GUI prompt failed: {e}")
            # Fallback to terminal
            return self._prompt_terminal(task_name)
    
    def _prompt_terminal(self, task_name: str) -> Optional[str]:
        """Show terminal prompt for justification."""
        try:
            print("\n" + "="*60)
            print("⚠️  TASK SWITCH DETECTED")
            print("="*60)
            print(f"\n{self.config.prompt_message}")
            print(f"You should be working on: {task_name}")
            print(f"\nYou have {self.config.timeout_seconds} seconds to respond.")
            print("Press Enter to skip or type your justification:\n")
            
            # Use select for timeout on Unix-like systems
            if sys.platform != 'win32':
                try:
                    import select
                    ready, _, _ = select.select([sys.stdin], [], [], self.config.timeout_seconds)
                    if ready:
                        justification = sys.stdin.readline().strip()
                        return justification if justification else None
                    else:
                        return None
                except Exception as e:
                    logger.error(f"Select failed: {e}")
                    # Fallback to simple input
                    justification = input("> ").strip()
                    return justification if justification else None
            else:
                # Simple input for Windows (no timeout)
                justification = input("> ").strip()
                return justification if justification else None
                
        except Exception as e:
            logger.error(f"Terminal prompt failed: {e}")
            return None
    
    def is_valid_justification(self, justification: str) -> bool:
        """
        Check if justification is valid (not empty, meaningful).
        
        Args:
            justification: User's justification text
            
        Returns:
            True if valid, False otherwise
        """
        if not justification or not justification.strip():
            return False
            
        # Check minimum length
        if len(justification.strip()) < 10:
            return False
            
        # Check for placeholder text
        invalid_phrases = [
            "asdf", "test", "nothing", "idk", "dunno",
            "none", "n/a", "na", "...", "???"
        ]
        
        justification_lower = justification.lower().strip()
        if justification_lower in invalid_phrases:
            return False
            
        return True
    
    def cancel_active_prompt(self) -> None:
        """Cancel any active justification prompt."""
        with self._prompt_lock:
            if self._active_prompt and self._active_prompt.is_alive():
                self._cancelled = True
                logger.info("Cancelled active justification prompt")


# Example usage
if __name__ == "__main__":
    # Example input: User browsing social media instead of coding
    # Example output: Prompt appears asking for justification
    
    manager = JustificationManager()
    
    print("Testing justification system...")
    print("A prompt will appear in 3 seconds...")
    time.sleep(3)
    
    result = manager.prompt_for_justification(
        task_name="Complete code review",
        callback=lambda r: print(f"\nCallback received: {r}")
    )
    
    print(f"\nResult: {result.status.value}")
    if result.justification_text:
        print(f"Justification: {result.justification_text}")
        print(f"Valid: {manager.is_valid_justification(result.justification_text)}")
    print(f"Response time: {result.response_time_seconds:.1f}s")