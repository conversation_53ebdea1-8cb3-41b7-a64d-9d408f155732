#!/usr/bin/env python3
"""Simple test runner for AI Reasoning module without pytest dependency."""

import sys
import traceback
from datetime import datetime
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from src.agents.ai_reasoning import AIConfig, AIReasoning
from shared.types import AIAnalysisResult, Screenshot, Task


def run_test(test_name, test_func):
    """Run a single test and report results."""
    try:
        test_func()
        print(f"✓ {test_name}")
        return True
    except AssertionError as e:
        print(f"✗ {test_name}")
        print(f"  AssertionError: {e}")
        return False
    except Exception as e:
        print(f"✗ {test_name}")
        print(f"  Error: {e}")
        traceback.print_exc()
        return False


def test_initialization():
    """Test AI Reasoning initialization."""
    # Test with default config
    ai1 = AIReasoning()
    assert ai1.config.model_name == "mock-gpt-4v"
    assert ai1.config.confidence_threshold_high == 0.8

    # Test with custom config
    custom_config = AIConfig(
        model_name="custom-model",
        confidence_threshold_high=0.9,
        confidence_threshold_medium=0.6
    )
    ai2 = AIReasoning(custom_config)
    assert ai2.config.model_name == "custom-model"
    assert ai2.config.confidence_threshold_high == 0.9


def test_analyze_screenshot_returns_valid_result():
    """Test that analyze_screenshot returns a valid AIAnalysisResult."""
    ai_reasoning = AIReasoning()

    task = Task(
        id="test-task-001",
        title="Complete Python module development",
        description="Implement and test the AI reasoning module",
        priority=5,
        status="active"
    )

    screenshot = Screenshot(
        id="test-screenshot-001",
        timestamp=datetime.now(),
        image_path="/tmp/test_screenshot.jpg",
        monitor_id=0,
        file_size=1024000
    )

    result = ai_reasoning.analyze_screenshot(screenshot, task)

    # Check result type
    assert isinstance(result, AIAnalysisResult)

    # Check required fields
    assert result.id is not None
    assert result.screenshot_id == screenshot.id
    assert result.task_id == task.id
    assert isinstance(result.is_aligned, bool)
    assert 0.0 <= result.confidence <= 1.0
    assert isinstance(result.reasoning, str)
    assert len(result.reasoning) > 0
    assert isinstance(result.suggestions, list)
    assert result.processing_time_ms > 0


def test_should_prompt_user():
    """Test should_prompt_user logic."""
    ai_reasoning = AIReasoning()

    # High confidence misalignment should prompt
    result1 = AIAnalysisResult(
        id="test-001",
        screenshot_id="screenshot-001",
        task_id="task-001",
        is_aligned=False,
        confidence=0.85,
        reasoning="User is on social media",
        suggestions=["Close social media"]
    )
    assert ai_reasoning.should_prompt_user(result1) is True

    # Low confidence shouldn't prompt
    result2 = AIAnalysisResult(
        id="test-002",
        screenshot_id="screenshot-002",
        task_id="task-002",
        is_aligned=False,
        confidence=0.45,
        reasoning="Uncertain",
        suggestions=[]
    )
    assert ai_reasoning.should_prompt_user(result2) is False

    # Aligned shouldn't prompt
    result3 = AIAnalysisResult(
        id="test-003",
        screenshot_id="screenshot-003",
        task_id="task-003",
        is_aligned=True,
        confidence=0.90,
        reasoning="User is coding",
        suggestions=[]
    )
    assert ai_reasoning.should_prompt_user(result3) is False


def test_get_model_info():
    """Test get_model_info returns expected structure."""
    ai_reasoning = AIReasoning()
    info = ai_reasoning.get_model_info()

    # Check required fields
    assert "model_name" in info
    assert "model_type" in info
    assert "status" in info
    assert "confidence_thresholds" in info
    assert "performance" in info
    assert "capabilities" in info
    assert info["confidence_thresholds"]["high"] == 0.8


def test_update_model_feedback():
    """Test that model feedback is accepted without errors."""
    ai_reasoning = AIReasoning()
    # Should not raise any exceptions
    ai_reasoning.update_model_feedback("analysis-001", was_accurate=True)
    ai_reasoning.update_model_feedback("analysis-002", was_accurate=False)


def main():
    """Run all tests."""
    print("=" * 60)
    print("Running AI Reasoning Tests")
    print("=" * 60)

    tests = [
        ("Initialization", test_initialization),
        ("Analyze Screenshot", test_analyze_screenshot_returns_valid_result),
        ("Should Prompt User", test_should_prompt_user),
        ("Get Model Info", test_get_model_info),
        ("Update Model Feedback", test_update_model_feedback),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        if run_test(test_name, test_func):
            passed += 1
        else:
            failed += 1

    print("=" * 60)
    print(f"Tests completed: {passed} passed, {failed} failed")
    print("=" * 60)

    return 0 if failed == 0 else 1


if __name__ == "__main__":
    sys.exit(main())
