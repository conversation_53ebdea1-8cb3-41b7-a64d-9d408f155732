#!/usr/bin/env python3
"""Example usage of the AI Reasoning module."""

import sys
import uuid
from datetime import datetime
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from src.agents.ai_reasoning import AIConfig, AIReasoning
from shared.types import Screenshot, Task


def create_example_task(title: str, description: str, priority: int = 3) -> Task:
    """Create an example task for demonstration."""
    return Task(
        id=f"task-{uuid.uuid4().hex[:8]}",
        title=title,
        description=description,
        priority=priority,
        status="active",
        tags=["example", "demo"]
    )


def create_example_screenshot(monitor_id: int = 0) -> Screenshot:
    """Create an example screenshot for demonstration."""
    return Screenshot(
        id=f"screenshot-{uuid.uuid4().hex[:8]}",
        timestamp=datetime.now(),
        image_path=f"/tmp/screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg",
        monitor_id=monitor_id,
        file_size=1024 * 512  # 512 KB
    )


def demonstrate_scenarios():
    """Demonstrate different AI reasoning scenarios."""
    print("=" * 80)
    print("AI Reasoning Module - Example Scenarios")
    print("=" * 80)

    # Initialize AI Reasoning
    config = AIConfig(
        model_name="mock-gpt-4v",
        confidence_threshold_high=0.8,
        confidence_threshold_medium=0.5
    )
    ai_reasoning = AIReasoning(config)

    # Display model info
    print("\nModel Information:")
    model_info = ai_reasoning.get_model_info()
    for key, value in model_info.items():
        if isinstance(value, dict):
            print(f"  {key}:")
            for k, v in value.items():
                print(f"    {k}: {v}")
        else:
            print(f"  {key}: {value}")

    print("\n" + "-" * 80)

    # Scenario 1: Programming task - likely aligned
    print("\nScenario 1: Programming Task")
    print("-" * 40)

    task1 = create_example_task(
        title="Implement AI reasoning module",
        description="Create the AI reasoning module with mock responses for analyzing screenshots and determining task alignment",
        priority=5
    )
    screenshot1 = create_example_screenshot()

    result1 = ai_reasoning.analyze_screenshot(screenshot1, task1)
    print(f"Task: {task1.title}")
    print(f"Priority: {task1.priority}")
    print("\nAnalysis Result:")
    print(f"  Aligned: {result1.is_aligned}")
    print(f"  Confidence: {result1.confidence}")
    print(f"  Should prompt user: {ai_reasoning.should_prompt_user(result1)}")
    print(f"  Processing time: {result1.processing_time_ms}ms")
    print(f"\nReasoning: {result1.reasoning}")
    if result1.suggestions:
        print("\nSuggestions:")
        for i, suggestion in enumerate(result1.suggestions, 1):
            print(f"  {i}. {suggestion}")

    print("\n" + "-" * 80)

    # Scenario 2: Documentation task - might detect distraction
    print("\nScenario 2: Documentation Task with Potential Distraction")
    print("-" * 40)

    task2 = create_example_task(
        title="Write technical documentation",
        description="Create comprehensive documentation for the new API endpoints including examples and best practices",
        priority=4
    )
    screenshot2 = create_example_screenshot()

    result2 = ai_reasoning.analyze_screenshot(screenshot2, task2)
    print(f"Task: {task2.title}")
    print(f"Priority: {task2.priority}")
    print("\nAnalysis Result:")
    print(f"  Aligned: {result2.is_aligned}")
    print(f"  Confidence: {result2.confidence}")
    print(f"  Should prompt user: {ai_reasoning.should_prompt_user(result2)}")
    print(f"  Processing time: {result2.processing_time_ms}ms")
    print(f"\nReasoning: {result2.reasoning}")
    if result2.suggestions:
        print("\nSuggestions:")
        for i, suggestion in enumerate(result2.suggestions, 1):
            print(f"  {i}. {suggestion}")

    print("\n" + "-" * 80)

    # Scenario 3: Generic task - uncertain analysis
    print("\nScenario 3: Generic Task with Uncertain Context")
    print("-" * 40)

    task3 = create_example_task(
        title="Complete project tasks",
        description="Work on various project-related activities and deliverables",
        priority=2
    )
    screenshot3 = create_example_screenshot()

    result3 = ai_reasoning.analyze_screenshot(screenshot3, task3)
    print(f"Task: {task3.title}")
    print(f"Priority: {task3.priority}")
    print("\nAnalysis Result:")
    print(f"  Aligned: {result3.is_aligned}")
    print(f"  Confidence: {result3.confidence}")
    print(f"  Should prompt user: {ai_reasoning.should_prompt_user(result3)}")
    print(f"  Processing time: {result3.processing_time_ms}ms")
    print(f"\nReasoning: {result3.reasoning}")
    if result3.suggestions:
        print("\nSuggestions:")
        for i, suggestion in enumerate(result3.suggestions, 1):
            print(f"  {i}. {suggestion}")

    print("\n" + "-" * 80)

    # Demonstrate feedback mechanism
    print("\nDemonstrating Feedback Mechanism")
    print("-" * 40)

    print(f"Providing positive feedback for analysis: {result1.id}")
    ai_reasoning.update_model_feedback(result1.id, was_accurate=True)

    print(f"Providing negative feedback for analysis: {result2.id}")
    ai_reasoning.update_model_feedback(result2.id, was_accurate=False)

    print("\nFeedback recorded (in real implementation, this would improve future analyses)")

    print("\n" + "=" * 80)
    print("Example scenarios completed!")
    print("=" * 80)


if __name__ == "__main__":
    demonstrate_scenarios()
