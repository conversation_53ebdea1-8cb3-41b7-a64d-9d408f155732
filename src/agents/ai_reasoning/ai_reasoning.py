#!/usr/bin/env python3
"""AI Reasoning module for analyzing screenshots and determining task alignment."""

import random
import time
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Any

from shared.types import AIAnalysisResult, Screenshot, Task, get_logger

logger = get_logger(__name__)


@dataclass
class AIConfig:
    """Configuration for AI reasoning module."""
    model_name: str = "mock-gpt-4v"
    confidence_threshold_high: float = 0.8
    confidence_threshold_medium: float = 0.5
    max_processing_time_ms: int = 3000
    enable_local_mode: bool = True
    api_timeout_seconds: int = 5


class AIReasoning:
    """AI Reasoning engine for screenshot analysis and task alignment detection."""

    def __init__(self, config: AIConfig | None = None):
        """Initialize with AI model configuration."""
        self.config = config or AIConfig()
        self._mock_scenarios = self._initialize_mock_scenarios()
        logger.info(f"Initialized AIReasoning with model: {self.config.model_name}")

    def _initialize_mock_scenarios(self) -> list[dict[str, Any]]:
        """Initialize mock scenarios for different analysis results."""
        return [
            # High confidence aligned scenarios
            {
                "keywords": ["code", "programming", "development", "IDE", "terminal"],
                "is_aligned": True,
                "confidence_range": (0.85, 0.95),
                "reasoning_template": "Screenshot shows {app} with {content}. This directly aligns with the task '{task_title}' which requires {task_desc}.",
                "suggestions": [],
                "detected_elements": ["Visual Studio Code", "Python code", "Terminal window"]
            },
            {
                "keywords": ["document", "writing", "report", "presentation"],
                "is_aligned": True,
                "confidence_range": (0.80, 0.90),
                "reasoning_template": "User is actively working in {app} on {content}. This aligns with the documentation/writing aspects of '{task_title}'.",
                "suggestions": ["Consider saving your work regularly", "Remember to commit changes if version controlled"],
                "detected_elements": ["Microsoft Word", "Google Docs", "Document editor"]
            },

            # High confidence misaligned scenarios
            {
                "keywords": ["social", "media", "facebook", "twitter", "instagram"],
                "is_aligned": False,
                "confidence_range": (0.85, 0.95),
                "reasoning_template": "Screenshot shows {app} which is a social media platform. This does not align with the current task '{task_title}' which requires focused work on {task_desc}.",
                "suggestions": [
                    "Close social media tabs to minimize distractions",
                    "Consider using a website blocker during work hours",
                    "Return to your task-related applications"
                ],
                "detected_elements": ["Facebook", "Twitter/X", "Instagram", "Social media feed"]
            },
            {
                "keywords": ["video", "youtube", "netflix", "streaming", "entertainment"],
                "is_aligned": False,
                "confidence_range": (0.80, 0.92),
                "reasoning_template": "User is watching {content} on {app}. This entertainment activity is not aligned with the task '{task_title}'.",
                "suggestions": [
                    "Pause the video and return to your work",
                    "Schedule entertainment for break times",
                    "Focus on completing your current task first"
                ],
                "detected_elements": ["YouTube", "Netflix", "Video player", "Entertainment content"]
            },

            # Medium confidence scenarios
            {
                "keywords": ["browser", "research", "stackoverflow", "documentation"],
                "is_aligned": True,
                "confidence_range": (0.60, 0.75),
                "reasoning_template": "Browser shows {content} which could be research related to '{task_title}'. Unable to determine exact relevance without more context.",
                "suggestions": [
                    "If this is task-related research, consider bookmarking important pages",
                    "Try to limit research time to stay productive"
                ],
                "detected_elements": ["Web browser", "Technical documentation", "Search results"]
            },
            {
                "keywords": ["email", "slack", "teams", "communication"],
                "is_aligned": None,  # Uncertain
                "confidence_range": (0.55, 0.70),
                "reasoning_template": "Communication tool {app} is open. This may or may not be related to '{task_title}' depending on the conversation context.",
                "suggestions": [
                    "If discussing the current task, great! Otherwise, consider deferring non-urgent messages",
                    "Set your status to 'focused work' to minimize interruptions"
                ],
                "detected_elements": ["Email client", "Slack", "Microsoft Teams", "Chat application"]
            },

            # Low confidence scenario
            {
                "keywords": ["desktop", "multiple", "unclear", "blank"],
                "is_aligned": None,
                "confidence_range": (0.25, 0.45),
                "reasoning_template": "Screenshot shows {content}. Unable to determine clear alignment with task '{task_title}' due to limited visible information.",
                "suggestions": [
                    "Ensure you're working on task-related activities",
                    "Consider organizing your workspace for better focus"
                ],
                "detected_elements": ["Desktop", "Multiple windows", "Unclear content"]
            }
        ]

    def analyze_screenshot(self, screenshot: Screenshot, task: Task) -> AIAnalysisResult:
        """
        Main analysis method to determine if screenshot aligns with current task.
        
        Args:
            screenshot: The screenshot to analyze
            task: The current active task
            
        Returns:
            AIAnalysisResult with alignment decision and reasoning
        """
        start_time = time.time()

        # Simulate processing time
        processing_delay = random.uniform(0.5, 2.0)
        time.sleep(processing_delay)

        # Select a mock scenario based on task keywords
        scenario = self._select_scenario(task)

        # Generate confidence score
        confidence = random.uniform(*scenario["confidence_range"])

        # Determine alignment
        if scenario["is_aligned"] is None:
            # For uncertain scenarios, randomly decide with bias toward confidence
            is_aligned = random.random() > (1 - confidence)
        else:
            is_aligned = scenario["is_aligned"]

        # Generate reasoning
        reasoning = self._generate_reasoning(scenario, task)

        # Get suggestions
        suggestions = scenario["suggestions"] if not is_aligned or confidence < 0.7 else []

        # Calculate processing time
        processing_time_ms = int((time.time() - start_time) * 1000)

        result = AIAnalysisResult(
            id=f"analysis-{uuid.uuid4().hex[:8]}",
            screenshot_id=screenshot.id,
            task_id=task.id,
            is_aligned=is_aligned,
            confidence=round(confidence, 3),
            reasoning=reasoning,
            suggestions=suggestions,
            processing_time_ms=processing_time_ms
        )

        logger.info(f"Analysis complete: aligned={is_aligned}, confidence={confidence:.3f}, "
                   f"processing_time={processing_time_ms}ms")

        return result

    def _select_scenario(self, task: Task) -> dict[str, Any]:
        """Select a mock scenario based on task description."""
        task_text = f"{task.title} {task.description}".lower()

        # Check for keyword matches
        for scenario in self._mock_scenarios:
            if any(keyword in task_text for keyword in scenario["keywords"][:2]):
                return scenario

        # If no specific match, randomly select based on task priority
        if task.priority >= 4:
            # High priority tasks more likely to detect misalignment
            weights = [0.3, 0.3, 0.5, 0.5, 0.3, 0.3, 0.2]
        else:
            # Lower priority tasks more uncertain
            weights = [0.4, 0.4, 0.2, 0.2, 0.5, 0.5, 0.4]

        return random.choices(self._mock_scenarios, weights=weights)[0]

    def _generate_reasoning(self, scenario: dict[str, Any], task: Task) -> str:
        """Generate reasoning explanation based on scenario and task."""
        # Mock detected elements
        app = random.choice(scenario["detected_elements"])
        content = random.choice([
            "active work", "relevant content", "multiple windows",
            "various applications", "mixed content"
        ])

        reasoning = scenario["reasoning_template"].format(
            app=app,
            content=content,
            task_title=task.title,
            task_desc=task.description[:50] + "..." if len(task.description) > 50 else task.description
        )

        return reasoning

    def should_prompt_user(self, result: AIAnalysisResult) -> bool:
        """
        Determine if user intervention is needed based on analysis results.
        
        Args:
            result: The AI analysis result
            
        Returns:
            True if user should be prompted, False otherwise
        """
        # Always prompt for misalignment with high confidence
        if not result.is_aligned and result.confidence >= self.config.confidence_threshold_high:
            return True

        # Prompt for medium confidence misalignment
        if not result.is_aligned and result.confidence >= self.config.confidence_threshold_medium:
            return True

        # Don't prompt for low confidence or aligned scenarios
        return False

    def get_model_info(self) -> dict[str, Any]:
        """Return current model configuration and status."""
        return {
            "model_name": self.config.model_name,
            "model_type": "mock",
            "status": "active",
            "confidence_thresholds": {
                "high": self.config.confidence_threshold_high,
                "medium": self.config.confidence_threshold_medium
            },
            "performance": {
                "average_processing_time_ms": 1500,
                "accuracy_estimate": 0.88,
                "total_analyses": random.randint(100, 1000)
            },
            "capabilities": [
                "screenshot_analysis",
                "task_alignment_detection",
                "confidence_scoring",
                "suggestion_generation"
            ],
            "last_updated": datetime.now().isoformat()
        }

    def update_model_feedback(self, analysis_id: str, was_accurate: bool) -> None:
        """
        Accept user feedback for model improvement.
        
        Args:
            analysis_id: The ID of the analysis to provide feedback for
            was_accurate: Whether the user agrees with the analysis
        """
        logger.info(f"Received feedback for analysis {analysis_id}: "
                   f"accurate={was_accurate}")

        # In a real implementation, this would:
        # 1. Store feedback in a database
        # 2. Use for model fine-tuning
        # 3. Update accuracy metrics
        # 4. Adjust confidence thresholds if needed

        # Mock implementation just logs the feedback
        feedback_type = "positive" if was_accurate else "negative"
        logger.debug(f"Feedback stored: {feedback_type} for analysis {analysis_id}")
