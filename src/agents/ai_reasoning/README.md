# AI Reasoning Module

This module implements the AI Reasoning & Enforcement feature for the Productivity Guard system. It analyzes screenshots to determine whether user activity aligns with their assigned tasks.

## Features

- **Mock AI Analysis**: Simulates AI-powered screenshot analysis without requiring actual API calls
- **Confidence Scoring**: Provides confidence levels (0.0-1.0) for each analysis
- **Smart Prompting**: Determines when user intervention is needed based on confidence thresholds
- **Natural Language Reasoning**: Generates human-readable explanations for decisions
- **Actionable Suggestions**: Provides specific recommendations when misalignment is detected

## Mock Scenarios

The module includes 7 pre-configured scenarios:

1. **Programming/Development** - High confidence aligned
2. **Document Writing** - High confidence aligned  
3. **Social Media** - High confidence misaligned
4. **Video/Entertainment** - High confidence misaligned
5. **Browser/Research** - Medium confidence, context-dependent
6. **Communication Tools** - Medium confidence, uncertain
7. **Desktop/Unclear** - Low confidence

## Usage

```python
from ai_reasoning import AIReasoning, AIConfig
from shared.types import Screenshot, Task

# Initialize with custom config
config = AIConfig(
    model_name="mock-gpt-4v",
    confidence_threshold_high=0.8,
    confidence_threshold_medium=0.5
)
ai = AIReasoning(config)

# Analyze a screenshot
result = ai.analyze_screenshot(screenshot, task)

# Check if user should be prompted
if ai.should_prompt_user(result):
    print(f"Alert: {result.reasoning}")
    for suggestion in result.suggestions:
        print(f"- {suggestion}")
```

## Running Examples

```bash
python3 -m ai_reasoning
```

## Running Tests

```bash
python3 ai_reasoning/test_runner.py
```

## Configuration

- **High Confidence**: >= 0.8 - Automated decisions allowed
- **Medium Confidence**: 0.5-0.79 - Suggestions provided, user confirmation preferred
- **Low Confidence**: < 0.5 - Analysis marked as uncertain, no automated actions

## Future Enhancements

- Replace mock responses with actual AI model integration
- Add support for multiple AI providers (OpenAI, Anthropic, local models)
- Implement learning from user feedback
- Add industry-specific task understanding