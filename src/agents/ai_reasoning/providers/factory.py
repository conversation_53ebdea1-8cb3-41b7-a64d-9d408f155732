"""Factory for creating LLM providers."""

import os
from typing import Optional

from .base import <PERSON><PERSON><PERSON><PERSON>


def create_provider(
    provider_name: str, 
    model_name: Optional[str] = None,
    api_key: Optional[str] = None
) -> LLMProvider:
    """Create an LLM provider instance.
    
    Args:
        provider_name: Name of the provider ('openai', 'anthropic')
        model_name: Optional model name override
        api_key: Optional API key override (will use env var if not provided)
    
    Returns:
        Configured LLM provider instance
    
    Raises:
        ValueError: If provider is not supported or API key is missing
        ImportError: If required libraries are not installed
    """
    provider_name = provider_name.lower()
    
    if provider_name == "openai":
        from .openai_provider import OpenAIProvider
        
        api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")
        
        model_name = model_name or "gpt-4-vision-preview"
        return OpenAIProvider(api_key=api_key, model_name=model_name)
    
    elif provider_name == "anthropic":
        from .anthropic_provider import AnthropicProvider
        
        api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            raise ValueError("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")
        
        model_name = model_name or "claude-3-opus-20240229"
        return AnthropicProvider(api_key=api_key, model_name=model_name)
    
    else:
        raise ValueError(f"Unsupported provider: {provider_name}. Supported: openai, anthropic")


def get_available_providers() -> list[str]:
    """Get list of available providers based on installed libraries and API keys."""
    available = []
    
    # Check OpenAI
    try:
        import openai
        if os.getenv("OPENAI_API_KEY"):
            available.append("openai")
    except ImportError:
        pass
    
    # Check Anthropic
    try:
        import anthropic
        if os.getenv("ANTHROPIC_API_KEY"):
            available.append("anthropic")
    except ImportError:
        pass
    
    return available


def create_best_available_provider(preferred_provider: Optional[str] = None) -> LLMProvider:
    """Create the best available provider.
    
    Args:
        preferred_provider: Preferred provider name, will fallback if not available
    
    Returns:
        Best available LLM provider
    
    Raises:
        RuntimeError: If no providers are available
    """
    available = get_available_providers()
    
    if not available:
        raise RuntimeError(
            "No LLM providers available. Install libraries and set API keys:\n"
            "- OpenAI: uv add openai && export OPENAI_API_KEY=your_key\n"
            "- Anthropic: uv add anthropic && export ANTHROPIC_API_KEY=your_key"
        )
    
    # Use preferred if available
    if preferred_provider and preferred_provider.lower() in available:
        return create_provider(preferred_provider)
    
    # Fallback order: anthropic (better vision), openai
    if "anthropic" in available:
        return create_provider("anthropic")
    elif "openai" in available:
        return create_provider("openai")
    else:
        # Should not reach here given the check above
        raise RuntimeError("No providers available")
