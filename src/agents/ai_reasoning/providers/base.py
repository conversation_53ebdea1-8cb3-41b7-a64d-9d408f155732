"""Base classes for LLM providers."""

import base64
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional
from pathlib import Path

from PIL import Image


@dataclass
class VisionAnalysisRequest:
    """Request for vision-based analysis."""
    image_path: str
    task_title: str
    task_description: str
    task_priority: int
    prompt_template: str
    max_tokens: int = 1000
    temperature: float = 0.3


@dataclass 
class VisionAnalysisResponse:
    """Response from vision analysis."""
    is_aligned: bool
    confidence: float  # 0.0 to 1.0
    reasoning: str
    suggestions: list[str]
    detected_elements: list[str]
    processing_time_ms: int
    model_used: str
    raw_response: str


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, api_key: str, model_name: str):
        self.api_key = api_key
        self.model_name = model_name
    
    @abstractmethod
    async def analyze_image(self, request: VisionAnalysisRequest) -> VisionAnalysisResponse:
        """Analyze an image for task alignment."""
        pass
    
    def encode_image_base64(self, image_path: str, max_size: tuple[int, int] = (1024, 1024)) -> str:
        """Encode image to base64 with optional resizing."""
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize if too large
                if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # Save to bytes
                import io
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_bytes = buffer.getvalue()
                
                return base64.b64encode(img_bytes).decode('utf-8')
        except Exception as e:
            raise ValueError(f"Failed to encode image {image_path}: {e}")
    
    def validate_image(self, image_path: str) -> bool:
        """Validate that image exists and is readable."""
        try:
            path = Path(image_path)
            if not path.exists():
                return False
            
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception:
            return False
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """Get the name of this provider."""
        pass
    
    @abstractmethod
    def get_model_info(self) -> dict[str, Any]:
        """Get information about the model."""
        pass
