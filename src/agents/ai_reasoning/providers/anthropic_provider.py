"""Anthropic Claude provider implementation."""

import json
import time
from typing import Any

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

from .base import LL<PERSON><PERSON>ider, VisionAnalysisRequest, VisionAnalysisResponse


class AnthropicProvider(LLMProvider):
    """Anthropic Claude provider for vision analysis."""
    
    def __init__(self, api_key: str, model_name: str = "claude-3-opus-20240229"):
        super().__init__(api_key, model_name)
        if not ANTHROPIC_AVAILABLE:
            raise ImportError("Anthropic library not available. Install with: uv add anthropic")
        
        self.client = anthropic.Anthropic(api_key=api_key)
    
    async def analyze_image(self, request: VisionAnalysisRequest) -> VisionAnalysisResponse:
        """Analyze image using Anthropic Claude."""
        start_time = time.time()
        
        # Validate image
        if not self.validate_image(request.image_path):
            raise ValueError(f"Invalid image: {request.image_path}")
        
        # Encode image
        base64_image = self.encode_image_base64(request.image_path)
        
        # Build prompt
        prompt = self._build_prompt(request)
        
        try:
            response = self.client.messages.create(
                model=self.model_name,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/jpeg",
                                    "data": base64_image
                                }
                            },
                            {
                                "type": "text",
                                "text": prompt
                            }
                        ]
                    }
                ]
            )
            
            processing_time_ms = int((time.time() - start_time) * 1000)
            raw_response = response.content[0].text
            
            # Parse response
            return self._parse_response(raw_response, processing_time_ms)
            
        except Exception as e:
            raise RuntimeError(f"Anthropic API error: {e}")
    
    def _build_prompt(self, request: VisionAnalysisRequest) -> str:
        """Build the analysis prompt."""
        return f"""
You are an AI assistant analyzing a screenshot to determine if the user's current activity aligns with their assigned task.

TASK INFORMATION:
- Title: {request.task_title}
- Description: {request.task_description}
- Priority: {request.task_priority}/5

ANALYSIS INSTRUCTIONS:
1. Examine the screenshot carefully
2. Identify what applications, websites, or activities are visible
3. Determine if the visible activity aligns with the assigned task
4. Provide a confidence score (0.0 to 1.0) for your assessment
5. Give specific reasoning for your decision
6. If not aligned, suggest 2-3 specific actions to refocus

RESPONSE FORMAT (JSON):
{{
    "is_aligned": boolean,
    "confidence": float (0.0-1.0),
    "reasoning": "detailed explanation of what you see and why it does/doesn't align",
    "suggestions": ["action 1", "action 2", "action 3"],
    "detected_elements": ["app/website 1", "app/website 2", "activity type"]
}}

Respond ONLY with valid JSON. Be specific about what you observe in the screenshot.
"""
    
    def _parse_response(self, raw_response: str, processing_time_ms: int) -> VisionAnalysisResponse:
        """Parse the LLM response into structured data."""
        try:
            # Try to extract JSON from response
            if "```json" in raw_response:
                json_start = raw_response.find("```json") + 7
                json_end = raw_response.find("```", json_start)
                json_str = raw_response[json_start:json_end].strip()
            elif raw_response.strip().startswith("{"):
                json_str = raw_response.strip()
            else:
                # Fallback: try to find JSON-like content
                import re
                json_match = re.search(r'\{.*\}', raw_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                else:
                    raise ValueError("No JSON found in response")
            
            data = json.loads(json_str)
            
            return VisionAnalysisResponse(
                is_aligned=bool(data.get("is_aligned", False)),
                confidence=float(data.get("confidence", 0.5)),
                reasoning=str(data.get("reasoning", "No reasoning provided")),
                suggestions=list(data.get("suggestions", [])),
                detected_elements=list(data.get("detected_elements", [])),
                processing_time_ms=processing_time_ms,
                model_used=self.model_name,
                raw_response=raw_response
            )
            
        except Exception as e:
            # Fallback response if parsing fails
            return VisionAnalysisResponse(
                is_aligned=False,
                confidence=0.3,
                reasoning=f"Failed to parse LLM response: {e}. Raw: {raw_response[:200]}...",
                suggestions=["Check the AI analysis system", "Try again"],
                detected_elements=["Unknown"],
                processing_time_ms=processing_time_ms,
                model_used=self.model_name,
                raw_response=raw_response
            )
    
    def get_provider_name(self) -> str:
        return "Anthropic"
    
    def get_model_info(self) -> dict[str, Any]:
        return {
            "provider": "Anthropic",
            "model": self.model_name,
            "capabilities": ["vision", "text_analysis", "json_output"],
            "max_image_size": "5MB",
            "supported_formats": ["PNG", "JPEG", "GIF", "WebP"]
        }
