"""Image processing utilities for AI analysis."""

import os
from pathlib import Path
from typing import Op<PERSON>, <PERSON>ple

from PIL import Image


class ImageProcessor:
    """Utility class for image processing operations."""
    
    @staticmethod
    def validate_image_file(image_path: str) -> bool:
        """Validate that an image file exists and is readable.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            True if image is valid, False otherwise
        """
        try:
            path = Path(image_path)
            if not path.exists() or not path.is_file():
                return False
            
            # Try to open and verify the image
            with Image.open(image_path) as img:
                img.verify()
            
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_image_info(image_path: str) -> dict:
        """Get information about an image file.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary with image information
        """
        try:
            with Image.open(image_path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "format": img.format,
                    "mode": img.mode,
                    "file_size": os.path.getsize(image_path),
                    "has_transparency": img.mode in ("RGBA", "LA") or "transparency" in img.info
                }
        except Exception as e:
            return {"error": str(e)}
    
    @staticmethod
    def resize_image_for_api(
        image_path: str, 
        max_size: Tuple[int, int] = (1024, 1024),
        quality: int = 85,
        output_path: Optional[str] = None
    ) -> str:
        """Resize image for API consumption while maintaining aspect ratio.
        
        Args:
            image_path: Path to the original image
            max_size: Maximum dimensions (width, height)
            quality: JPEG quality (1-100)
            output_path: Optional output path, defaults to temp file
            
        Returns:
            Path to the resized image
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Calculate new size maintaining aspect ratio
                original_size = img.size
                if original_size[0] <= max_size[0] and original_size[1] <= max_size[1]:
                    # No resizing needed
                    if output_path:
                        img.save(output_path, "JPEG", quality=quality)
                        return output_path
                    else:
                        return image_path
                
                # Calculate new dimensions
                ratio = min(max_size[0] / original_size[0], max_size[1] / original_size[1])
                new_size = (int(original_size[0] * ratio), int(original_size[1] * ratio))
                
                # Resize image
                resized_img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # Save resized image
                if not output_path:
                    # Create temp file
                    import tempfile
                    temp_dir = Path(image_path).parent / ".tmp"
                    temp_dir.mkdir(exist_ok=True)
                    output_path = str(temp_dir / f".tmp_resized_{Path(image_path).stem}.jpg")
                
                resized_img.save(output_path, "JPEG", quality=quality)
                return output_path
                
        except Exception as e:
            raise ValueError(f"Failed to resize image {image_path}: {e}")
    
    @staticmethod
    def estimate_api_cost(image_path: str, provider: str = "openai") -> dict:
        """Estimate API cost for image analysis.
        
        Args:
            image_path: Path to the image
            provider: Provider name ("openai", "anthropic")
            
        Returns:
            Dictionary with cost estimation
        """
        try:
            info = ImageProcessor.get_image_info(image_path)
            if "error" in info:
                return {"error": info["error"]}
            
            # Rough cost estimates (as of 2024)
            if provider.lower() == "openai":
                # GPT-4V pricing is roughly $0.01 per image for high detail
                base_cost = 0.01
                if info["width"] * info["height"] > 1024 * 1024:
                    base_cost *= 1.5  # Higher cost for larger images
            elif provider.lower() == "anthropic":
                # Claude 3 pricing varies by model and tokens
                base_cost = 0.015  # Rough estimate
            else:
                base_cost = 0.01  # Default estimate
            
            return {
                "estimated_cost_usd": base_cost,
                "image_size": f"{info['width']}x{info['height']}",
                "file_size_mb": info["file_size"] / (1024 * 1024),
                "provider": provider
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    @staticmethod
    def cleanup_temp_files(directory: str = None):
        """Clean up temporary image files.
        
        Args:
            directory: Directory to clean, defaults to current working directory
        """
        try:
            if directory is None:
                directory = "."
            
            temp_dir = Path(directory) / ".tmp"
            if temp_dir.exists():
                for file in temp_dir.glob(".tmp_*"):
                    try:
                        file.unlink()
                    except Exception:
                        pass  # Ignore errors for individual files
                        
        except Exception:
            pass  # Ignore cleanup errors
