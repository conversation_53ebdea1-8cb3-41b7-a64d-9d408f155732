"""
Cross-platform notification handler for Productivity Guard.

Uses plyer library for platform-independent notifications.
"""

import logging
import platform

try:
    from plyer import notification
    PLYER_AVAILABLE = True
except ImportError:
    PLYER_AVAILABLE = False
    logging.warning("plyer not available - notifications will be logged only")

# Platform-specific imports for advanced features
system = platform.system()

if system == "Darwin":  # macOS
    try:
        import subprocess

        import objc
        from Foundation import NSUserNotification, NSUserNotificationCenter
        MACOS_NATIVE = True
    except ImportError:
        MACOS_NATIVE = False
elif system == "Windows":
    try:
        from win10toast import ToastNotifier
        WIN_TOAST = True
    except ImportError:
        WIN_TOAST = False
elif system == "Linux":
    try:
        import gi
        gi.require_version('Notify', '0.7')
        from gi.repository import Notify
        Notify.init("Productivity Guard")
        LINUX_NOTIFY = True
    except ImportError:
        LINUX_NOTIFY = False


class NotificationHandler:
    """Handles cross-platform notifications with fallback options."""

    def __init__(self):
        """Initialize the notification handler."""
        self.logger = logging.getLogger(__name__)
        self.platform = platform.system()
        self.notification_queue = []
        self.acknowledgment_pending = False

        # Platform-specific notifiers
        if system == "Windows" and WIN_TOAST:
            self.win_toaster = ToastNotifier()

        self.logger.info(f"NotificationHandler initialized on {self.platform}")
        self.logger.info(f"Available backends: plyer={PLYER_AVAILABLE}, "
                        f"macOS_native={MACOS_NATIVE if system == 'Darwin' else 'N/A'}, "
                        f"win_toast={WIN_TOAST if system == 'Windows' else 'N/A'}, "
                        f"linux_notify={LINUX_NOTIFY if system == 'Linux' else 'N/A'}")

    def send_notification(self, title: str, message: str, urgency: str = "low",
                         sound: bool = False, require_acknowledgment: bool = False,
                         timeout: int = 10) -> bool:
        """
        Send a notification using the best available method.
        
        Args:
            title: Notification title
            message: Notification message
            urgency: Urgency level ('low', 'medium', 'high')
            sound: Whether to play a sound
            require_acknowledgment: Whether to wait for user acknowledgment
            timeout: Timeout in seconds (not all platforms support this)
        
        Returns:
            True if notification was sent successfully
        """
        try:
            # Log the notification regardless of display method
            self.logger.info(f"Notification [{urgency}]: {title} - {message}")

            # Try platform-specific methods first for better features
            if self.platform == "Darwin" and MACOS_NATIVE:
                return self._send_macos_notification(title, message, sound, urgency)
            elif self.platform == "Windows" and WIN_TOAST:
                return self._send_windows_notification(title, message, sound, timeout)
            elif self.platform == "Linux" and LINUX_NOTIFY:
                return self._send_linux_notification(title, message, urgency, timeout)
            elif PLYER_AVAILABLE:
                return self._send_plyer_notification(title, message, timeout)
            else:
                # Fallback to console output
                self._fallback_notification(title, message, urgency)
                return True

        except Exception as e:
            self.logger.error(f"Failed to send notification: {e}")
            self._fallback_notification(title, message, urgency)
            return False

    def _send_plyer_notification(self, title: str, message: str, timeout: int) -> bool:
        """Send notification using plyer."""
        try:
            notification.notify(
                title=title,
                message=message,
                timeout=timeout,
                app_name="Productivity Guard"
            )
            return True
        except Exception as e:
            self.logger.error(f"Plyer notification failed: {e}")
            return False

    def _send_macos_notification(self, title: str, message: str, sound: bool, urgency: str) -> bool:
        """Send notification on macOS using native APIs."""
        try:
            # Use osascript for better control
            script = f'''
            display notification "{message}" with title "{title}" sound name {"default" if sound else ""}
            '''
            subprocess.run(['osascript', '-e', script], check=True)
            return True
        except Exception as e:
            self.logger.error(f"macOS notification failed: {e}")
            return False

    def _send_windows_notification(self, title: str, message: str, sound: bool, timeout: int) -> bool:
        """Send notification on Windows using win10toast."""
        try:
            # Windows toast notifications
            self.win_toaster.show_toast(
                title=title,
                msg=message,
                duration=timeout,
                threaded=True
            )

            if sound:
                import winsound
                winsound.MessageBeep()

            return True
        except Exception as e:
            self.logger.error(f"Windows notification failed: {e}")
            return False

    def _send_linux_notification(self, title: str, message: str, urgency: str, timeout: int) -> bool:
        """Send notification on Linux using libnotify."""
        try:
            notification = Notify.Notification.new(title, message, "dialog-information")

            # Set urgency
            urgency_map = {
                'low': Notify.Urgency.LOW,
                'medium': Notify.Urgency.NORMAL,
                'high': Notify.Urgency.CRITICAL
            }
            notification.set_urgency(urgency_map.get(urgency, Notify.Urgency.NORMAL))

            # Set timeout (in milliseconds)
            notification.set_timeout(timeout * 1000)

            # Show notification
            notification.show()
            return True
        except Exception as e:
            self.logger.error(f"Linux notification failed: {e}")
            return False

    def _fallback_notification(self, title: str, message: str, urgency: str) -> None:
        """Fallback notification method using console output."""
        separator = "=" * 60
        urgency_marker = {
            'low': '[INFO]',
            'medium': '[WARNING]',
            'high': '[ALERT]'
        }.get(urgency, '[NOTICE]')

        print(f"\n{separator}")
        print(f"{urgency_marker} {title}")
        print(separator)
        print(message)
        print(f"{separator}\n")

    def show_fullscreen_overlay(self, content: dict) -> None:
        """
        Display a fullscreen overlay for severe escalations.
        
        Args:
            content: Dictionary with overlay content
        """
        # For now, we'll use a prominent notification
        # In a full implementation, this would create an actual overlay window
        self.logger.warning("SEVERE ESCALATION - Fullscreen overlay requested")

        message = f"""
PRODUCTIVITY ALERT - SEVERE

Task: {content.get('task_title', 'Unknown')}
Time Off-Task: {content.get('time_off_task', 'Unknown')}
Impact: {content.get('impact', 'Significant productivity loss')}

Suggestions:
{chr(10).join('• ' + s for s in content.get('suggestions', ['Return to your task immediately']))}

This is a severe escalation. Please return to your task immediately.
        """

        # Send as high-priority notification
        self.send_notification(
            title="⚠️ SEVERE: Return to Task Immediately",
            message=message.strip(),
            urgency="high",
            sound=True,
            require_acknowledgment=True,
            timeout=30
        )

        # In a full implementation, we would:
        # 1. Create a semi-transparent fullscreen window
        # 2. Display the content in a modal dialog
        # 3. Require user interaction to dismiss
        # 4. Track acknowledgment time

    def play_notification_sound(self, urgency: str) -> None:
        """Play a notification sound based on urgency level."""
        try:
            if self.platform == "Darwin":
                # macOS system sounds
                sounds = {
                    'low': 'Tink',
                    'medium': 'Glass',
                    'high': 'Sosumi'
                }
                sound = sounds.get(urgency, 'Tink')
                subprocess.run(['afplay', f'/System/Library/Sounds/{sound}.aiff'])
            elif self.platform == "Windows":
                import winsound
                if urgency == 'high':
                    winsound.MessageBeep(winsound.MB_ICONEXCLAMATION)
                else:
                    winsound.MessageBeep()
            elif self.platform == "Linux":
                # Use paplay or aplay if available
                subprocess.run(['paplay', '/usr/share/sounds/freedesktop/stereo/message.oga'])
        except Exception as e:
            self.logger.debug(f"Could not play sound: {e}")

    def create_action_notification(self, title: str, message: str,
                                 actions: list[dict[str, str]]) -> str | None:
        """
        Create a notification with action buttons.
        
        Args:
            title: Notification title
            message: Notification message
            actions: List of action dictionaries with 'label' and 'action' keys
        
        Returns:
            Selected action or None
        """
        # This is a placeholder for rich notifications with actions
        # Full implementation would use platform-specific APIs
        self.logger.info(f"Action notification requested with actions: {actions}")

        # For now, just show a regular notification
        action_text = " | ".join(a['label'] for a in actions)
        extended_message = f"{message}\n\nActions: {action_text}"

        self.send_notification(title, extended_message, urgency="medium", sound=True)

        # In a full implementation, this would:
        # 1. Create a notification with clickable buttons
        # 2. Wait for user interaction
        # 3. Return the selected action
        return None

    def get_notification_history(self, limit: int = 50) -> list[dict]:
        """
        Get recent notification history.
        
        Args:
            limit: Maximum number of notifications to return
        
        Returns:
            List of notification records
        """
        # This would integrate with a persistent storage system
        # For now, return empty list
        return []

    def clear_all_notifications(self) -> None:
        """Clear all pending notifications."""
        self.notification_queue.clear()
        self.acknowledgment_pending = False
        self.logger.info("All notifications cleared")
