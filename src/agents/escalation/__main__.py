"""
Example escalation scenarios for Productivity Guard.

This module demonstrates how the escalation system works with various
task alignment patterns.
"""


# Add project root to path for imports
import sys
import time
from datetime import datetime
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.agents.escalation.escalation_manager import EscalationLevel, EscalationManager
from shared.types import AIAnalysisResult, Task


def create_mock_analysis(task_id: str, is_aligned: bool, confidence: float = 0.9) -> AIAnalysisResult:
    """Create a mock AI analysis result for testing."""
    return AIAnalysisResult(
        id=f"analysis-{datetime.now().timestamp()}",
        screenshot_id=f"screenshot-{datetime.now().timestamp()}",
        task_id=task_id,
        is_aligned=is_aligned,
        confidence=confidence,
        reasoning="Mock analysis for demonstration" if is_aligned else "User appears distracted",
        suggestions=[] if is_aligned else ["Close social media", "Focus on work"]
    )


def scenario_gradual_escalation(manager: EscalationManager):
    """Demonstrate gradual escalation from NONE to SEVERE."""
    print("\n" + "="*60)
    print("SCENARIO 1: Gradual Escalation")
    print("="*60)

    task = Task(
        id="task-001",
        title="Complete quarterly report",
        priority=5,
        description="Finish Q4 financial analysis",
        status="active"
    )

    task_context = {
        'title': task.title,
        'description': task.description,
        'suggestions': ["Open spreadsheet application", "Review financial data"]
    }

    # Simulate progressive distraction
    history = []
    for i in range(15):
        # Create misaligned result
        result = create_mock_analysis(task.id, is_aligned=False, confidence=0.85)
        history.insert(0, result)  # Add to front (newest first)

        # Check escalation
        level = manager.check_escalation_needed(history[-10:])  # Last 10 results

        print(f"\nIteration {i+1}:")
        print(f"  - Consecutive misaligned: {manager.consecutive_misaligned}")
        print(f"  - Current level: {level.name}")

        # Execute escalation if needed
        if level != EscalationLevel.NONE:
            manager.execute_escalation(level, task_context)

        time.sleep(0.5)  # Brief pause for demonstration

        # Stop at SEVERE
        if level == EscalationLevel.SEVERE:
            print("\n⚠️  Maximum escalation reached!")
            break


def scenario_quick_recovery(manager: EscalationManager):
    """Demonstrate de-escalation when user returns to task."""
    print("\n" + "="*60)
    print("SCENARIO 2: Quick Recovery")
    print("="*60)

    task = Task(
        id="task-002",
        title="Code review for PR #123",
        priority=4,
        description="Review and approve pull request",
        status="active"
    )

    task_context = {'title': task.title}

    # First, escalate to MODERATE
    history = []
    print("\nPhase 1: Getting distracted...")
    for i in range(7):
        result = create_mock_analysis(task.id, is_aligned=False)
        history.insert(0, result)
        level = manager.check_escalation_needed(history[-10:])
        print(f"  - Misaligned {i+1}: Level = {level.name}")

    manager.execute_escalation(level, task_context)

    # Then return to task
    print("\nPhase 2: Returning to task...")
    for i in range(5):
        result = create_mock_analysis(task.id, is_aligned=True)
        history.insert(0, result)
        level = manager.check_escalation_needed(history[-10:])
        print(f"  - Aligned {i+1}: Level = {level.name}")
        time.sleep(0.3)

    print("\n✅ Successfully de-escalated!")


def scenario_low_confidence(manager: EscalationManager):
    """Demonstrate handling of low confidence results."""
    print("\n" + "="*60)
    print("SCENARIO 3: Low Confidence Handling")
    print("="*60)

    task = Task(
        id="task-003",
        title="Research competitive analysis",
        priority=3,
        description="Analyze competitor products",
        status="active"
    )

    # Mix of high and low confidence misalignments
    history = []
    print("\nMixed confidence results:")

    confidence_levels = [0.9, 0.6, 0.8, 0.5, 0.9, 0.4, 0.95, 0.65, 0.9, 0.55]

    for i, confidence in enumerate(confidence_levels):
        result = create_mock_analysis(task.id, is_aligned=False, confidence=confidence)
        history.insert(0, result)
        level = manager.check_escalation_needed(history[-10:])

        print(f"\nResult {i+1}:")
        print(f"  - Confidence: {confidence:.2f}")
        print(f"  - Consecutive misaligned: {manager.consecutive_misaligned:.1f}")
        print(f"  - Level: {level.name}")

        time.sleep(0.3)


def scenario_break_time(manager: EscalationManager):
    """Demonstrate break time behavior."""
    print("\n" + "="*60)
    print("SCENARIO 4: Break Time Leniency")
    print("="*60)

    # Temporarily set current hour to break time
    original_config = manager.config.copy()
    current_hour = datetime.now().hour
    manager.config['break_hours'] = [current_hour]

    task = Task(
        id="task-004",
        title="Team meeting preparation",
        priority=4,
        description="Prepare slides for team meeting",
        status="active"
    )

    history = []
    print(f"\nCurrent hour ({current_hour}) is set as break time")
    print("Sending misaligned results during break...")

    for i in range(10):
        result = create_mock_analysis(task.id, is_aligned=False)
        history.insert(0, result)
        level = manager.check_escalation_needed(history[-10:])
        print(f"  - Misaligned {i+1}: Level = {level.name} (should stay NONE)")

    # Restore original config
    manager.config = original_config
    manager.reset_escalation()


def scenario_intermittent_focus(manager: EscalationManager):
    """Demonstrate behavior with intermittent focus patterns."""
    print("\n" + "="*60)
    print("SCENARIO 5: Intermittent Focus")
    print("="*60)

    task = Task(
        id="task-005",
        title="Debug production issue",
        priority=5,
        description="Fix critical bug in payment system",
        status="active"
    )

    task_context = {'title': task.title}

    # Pattern: distracted, focused, distracted, focused
    pattern = [False, False, False, True, False, False, True, False, False, False, True, True]
    history = []

    print("\nIntermittent focus pattern:")
    for i, is_aligned in enumerate(pattern):
        result = create_mock_analysis(task.id, is_aligned=is_aligned)
        history.insert(0, result)
        level = manager.check_escalation_needed(history[-10:])

        status = "✓ Aligned" if is_aligned else "✗ Misaligned"
        print(f"  {i+1}. {status} -> Level: {level.name} (consecutive misaligned: {int(manager.consecutive_misaligned)})")

        if level != EscalationLevel.NONE:
            manager.execute_escalation(level, task_context)

        time.sleep(0.3)


def main():
    """Run example escalation scenarios."""
    print("Productivity Guard - Escalation Mode Examples")
    print("=" * 60)

    # Create escalation manager with demo config
    config = {
        'mild_threshold': 3,
        'moderate_threshold': 6,
        'severe_threshold': 10,
        'aligned_for_deescalation': 3,
        'deescalation_cooldown_minutes': 5,
        'low_confidence_threshold': 0.7,
        'enable_sound_notifications': False,  # Quiet for demo
        'enable_browser_management': False    # No actual browser control for demo
    }

    manager = EscalationManager(config)

    scenarios = [
        ("Gradual Escalation", scenario_gradual_escalation),
        ("Quick Recovery", scenario_quick_recovery),
        ("Low Confidence Handling", scenario_low_confidence),
        ("Break Time Leniency", scenario_break_time),
        ("Intermittent Focus", scenario_intermittent_focus)
    ]

    print("\nAvailable scenarios:")
    for i, (name, _) in enumerate(scenarios, 1):
        print(f"  {i}. {name}")

    # Run all scenarios with user confirmation
    for i, (name, scenario_func) in enumerate(scenarios, 1):
        if i > 1:
            input(f"\nPress Enter to run scenario {i}: {name}")

        # Reset manager state between scenarios
        manager.reset_escalation()
        scenario_func(manager)

    # Show final statistics
    print("\n" + "="*60)
    print("ESCALATION STATISTICS")
    print("="*60)
    stats = manager.get_escalation_stats()
    print(f"Total escalations: {stats['total_escalations']}")
    print(f"By level: {stats['by_level']}")

    print("\n✅ All scenarios completed!")


if __name__ == "__main__":
    main()
