"""
Escalation Manager for Productivity Guard

Handles progressive interventions based on user task alignment.
"""

# Import shared types
import sys
from collections import deque
from datetime import datetime, timedelta
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent))
from shared.types import AIAnalysisResult, EscalationLevel, NotificationEvent, get_logger

# Import notification handler
from .notification_handler import NotificationHandler


class EscalationManager:
    """Manages escalation levels and interventions based on task alignment."""

    def __init__(self, config: dict | None = None):
        """
        Initialize the Escalation Manager.
        
        Args:
            config: Optional configuration dictionary
        """
        self.logger = get_logger(__name__)
        self.config = config or self._get_default_config()

        # Current escalation state
        self.current_level = EscalationLevel.NONE
        self.consecutive_misaligned = 0
        self.last_escalation_time = None
        self.escalation_history = deque(maxlen=100)  # Keep last 100 escalation events

        # Initialize notification handler
        self.notification_handler = NotificationHandler()

        # Thresholds for escalation (configurable)
        self.thresholds = {
            EscalationLevel.MILD: self.config.get('mild_threshold', 3),
            EscalationLevel.MODERATE: self.config.get('moderate_threshold', 6),
            EscalationLevel.SEVERE: self.config.get('severe_threshold', 10)
        }

        # De-escalation requirements
        self.aligned_count_for_deescalation = self.config.get('aligned_for_deescalation', 3)
        self.consecutive_aligned = 0

        self.logger.info("EscalationManager initialized with thresholds: %s", self.thresholds)

    def _get_default_config(self) -> dict:
        """Get default configuration."""
        return {
            'mild_threshold': 3,
            'moderate_threshold': 6,
            'severe_threshold': 10,
            'aligned_for_deescalation': 3,
            'deescalation_cooldown_minutes': 5,
            'low_confidence_threshold': 0.7,
            'break_hours': [12, 13],  # Lunch break
            'working_hours': (9, 18),  # 9 AM to 6 PM
            'enable_browser_management': True,
            'enable_sound_notifications': True
        }

    def check_escalation_needed(self, history: list[AIAnalysisResult]) -> EscalationLevel:
        """
        Analyze recent analysis history to determine appropriate escalation level.
        
        Parameters:
            history: List of recent AI analysis results (newest first)
        
        Returns:
            EscalationLevel enum value
        """
        if not history:
            return EscalationLevel.NONE

        # Get the most recent result
        latest_result = history[0]

        # Check if it's during break time or outside working hours
        if self._is_break_time() or not self._is_working_hours():
            self.logger.info("Break time or outside working hours - no escalation")
            return EscalationLevel.NONE

        # Handle low confidence results more leniently
        if latest_result.confidence < self.config['low_confidence_threshold']:
            self.logger.info("Low confidence result (%.2f) - being lenient", latest_result.confidence)
            # Don't count low confidence misalignments as strongly
            if not latest_result.is_aligned:
                self.consecutive_misaligned += 0.5
            else:
                self.consecutive_aligned += 1
        else:
            # High confidence result
            if latest_result.is_aligned:
                self.consecutive_aligned += 1
                self.consecutive_misaligned = 0
            else:
                self.consecutive_misaligned += 1
                self.consecutive_aligned = 0

        # Check for de-escalation
        if self.consecutive_aligned >= self.aligned_count_for_deescalation:
            return self._handle_deescalation()

        # Determine escalation level based on consecutive misalignments
        new_level = self._calculate_escalation_level()

        # Log if level is changing
        if new_level != self.current_level:
            self._log_escalation_change(self.current_level, new_level, latest_result)

        self.current_level = new_level
        return new_level

    def _calculate_escalation_level(self) -> EscalationLevel:
        """Calculate the appropriate escalation level based on misalignment count."""
        misaligned_count = int(self.consecutive_misaligned)

        if misaligned_count >= self.thresholds[EscalationLevel.SEVERE]:
            return EscalationLevel.SEVERE
        elif misaligned_count >= self.thresholds[EscalationLevel.MODERATE]:
            return EscalationLevel.MODERATE
        elif misaligned_count >= self.thresholds[EscalationLevel.MILD]:
            return EscalationLevel.MILD
        else:
            return EscalationLevel.NONE

    def _handle_deescalation(self) -> EscalationLevel:
        """Handle de-escalation when user returns to task."""
        if self.current_level == EscalationLevel.NONE:
            return EscalationLevel.NONE

        # Check if enough time has passed for full reset
        if (self.last_escalation_time and
            datetime.now() - self.last_escalation_time >
            timedelta(minutes=self.config['deescalation_cooldown_minutes'])):
            self.logger.info("Full de-escalation after cooldown period")
            self.reset_escalation()
            return EscalationLevel.NONE

        # Otherwise, reduce by one level
        new_level = EscalationLevel(max(0, self.current_level.value - 1))
        self.logger.info("De-escalating from %s to %s", self.current_level.name, new_level.name)
        return new_level

    def execute_escalation(self, level: EscalationLevel, task_context: dict | None = None) -> None:
        """
        Execute interventions for given escalation level.
        
        Args:
            level: The escalation level to execute
            task_context: Optional context about the current task
        """
        self.last_escalation_time = datetime.now()
        task_title = task_context.get('title', 'your current task') if task_context else 'your current task'

        if level == EscalationLevel.NONE:
            # No action needed
            return

        elif level == EscalationLevel.MILD:
            # Gentle notification
            message = f"Hey! Looks like you might be off track. Current task: {task_title}"
            self.send_notification(message, "low")
            self._update_system_tray("yellow")

        elif level == EscalationLevel.MODERATE:
            # Prominent notification with sound
            duration = self._format_duration(self.consecutive_misaligned * 5)  # Assuming 5-second intervals
            message = f"You've been off-task for {duration}. Time to refocus on: {task_title}"
            self.send_notification(message, "medium", require_acknowledgment=True)
            self._update_system_tray("orange")

            # Send to accountability partner if configured
            if task_context and task_context.get('accountability_partner'):
                self._notify_accountability_partner(task_context['accountability_partner'], message)

        elif level == EscalationLevel.SEVERE:
            # Maximum intervention
            self._execute_severe_intervention(task_title, task_context)
            self._update_system_tray("red")

        # Log the escalation event
        self._log_escalation_event(level, task_context)

    def _execute_severe_intervention(self, task_title: str, task_context: dict | None) -> None:
        """Execute severe level interventions."""
        # Display full-screen overlay
        duration = self._format_duration(self.consecutive_misaligned * 5)
        overlay_content = {
            'task_title': task_title,
            'time_off_task': duration,
            'impact': f"You've lost {self.consecutive_misaligned * 5} seconds of productivity",
            'suggestions': task_context.get('suggestions', []) if task_context else []
        }

        self.notification_handler.show_fullscreen_overlay(overlay_content)

        # Close non-productive browser tabs if enabled
        if self.config['enable_browser_management']:
            self._manage_browser_tabs(task_context)

        # Send urgent notification to accountability partner
        if task_context and task_context.get('accountability_partner'):
            message = f"URGENT: {task_title} - User has been off-task for {duration}"
            self._notify_accountability_partner(task_context['accountability_partner'], message, urgent=True)

    def send_notification(self, message: str, urgency: str, require_acknowledgment: bool = False) -> None:
        """
        Send notification with specified urgency level.
        
        Args:
            message: Notification message
            urgency: Urgency level ('low', 'medium', 'high')
            require_acknowledgment: Whether notification requires user acknowledgment
        """
        notification_event = NotificationEvent(
            id=f"notif-{datetime.now().timestamp()}",
            level=self.current_level,
            message=message,
            timestamp=datetime.now()
        )

        # Use notification handler to display the notification
        success = self.notification_handler.send_notification(
            title="Productivity Guard",
            message=message,
            urgency=urgency,
            sound=self.config['enable_sound_notifications'] and urgency != 'low',
            require_acknowledgment=require_acknowledgment
        )

        if success:
            self.logger.info("Notification sent: %s", message)
        else:
            self.logger.error("Failed to send notification")

        # Store notification event
        self.escalation_history.append(notification_event)

    def de_escalate(self, current_level: EscalationLevel) -> EscalationLevel:
        """Reduce escalation level when compliance improves."""
        if current_level == EscalationLevel.NONE:
            return EscalationLevel.NONE

        new_level = EscalationLevel(max(0, current_level.value - 1))
        self.current_level = new_level
        self.logger.info("De-escalated from %s to %s", current_level.name, new_level.name)
        return new_level

    def get_current_level(self) -> EscalationLevel:
        """Return current escalation level."""
        return self.current_level

    def reset_escalation(self) -> None:
        """Reset to NONE level and clear history."""
        self.current_level = EscalationLevel.NONE
        self.consecutive_misaligned = 0
        self.consecutive_aligned = 0
        self.last_escalation_time = None
        self.logger.info("Escalation state reset")

    def configure_thresholds(self, config: dict[str, int]) -> None:
        """
        Update escalation thresholds dynamically.
        
        Args:
            config: Dictionary with threshold values
        """
        if 'mild_threshold' in config:
            self.thresholds[EscalationLevel.MILD] = config['mild_threshold']
        if 'moderate_threshold' in config:
            self.thresholds[EscalationLevel.MODERATE] = config['moderate_threshold']
        if 'severe_threshold' in config:
            self.thresholds[EscalationLevel.SEVERE] = config['severe_threshold']

        self.logger.info("Updated thresholds: %s", self.thresholds)

    # Helper methods

    def _is_break_time(self) -> bool:
        """Check if current time is during break hours."""
        current_hour = datetime.now().hour
        return current_hour in self.config.get('break_hours', [])

    def _is_working_hours(self) -> bool:
        """Check if current time is within working hours."""
        current_hour = datetime.now().hour
        start, end = self.config.get('working_hours', (9, 18))
        return start <= current_hour < end

    def _format_duration(self, seconds: int) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{seconds} seconds"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes} minute{'s' if minutes > 1 else ''}"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours} hour{'s' if hours > 1 else ''} {minutes} minute{'s' if minutes > 1 else ''}"

    def _update_system_tray(self, color: str) -> None:
        """Update system tray icon color."""
        # This would integrate with system tray implementation
        self.logger.info("System tray updated to %s", color)

    def _notify_accountability_partner(self, partner_info: dict, message: str, urgent: bool = False) -> None:
        """Send notification to accountability partner."""
        # This would integrate with notification service (email, SMS, etc.)
        self.logger.info("Notifying accountability partner: %s", message)

    def _manage_browser_tabs(self, task_context: dict | None) -> None:
        """Close non-productive browser tabs."""
        # This would integrate with browser management module
        self.logger.info("Managing browser tabs for severe escalation")

    def _log_escalation_event(self, level: EscalationLevel, context: dict | None) -> None:
        """Log escalation event for analytics."""
        event = {
            'timestamp': datetime.now().isoformat(),
            'level': level.name,
            'consecutive_misaligned': self.consecutive_misaligned,
            'context': context
        }
        self.escalation_history.append(event)

    def _log_escalation_change(self, old_level: EscalationLevel, new_level: EscalationLevel,
                              result: AIAnalysisResult) -> None:
        """Log escalation level change."""
        self.logger.info(
            "Escalation changed: %s -> %s (consecutive misaligned: %d, confidence: %.2f)",
            old_level.name, new_level.name, self.consecutive_misaligned, result.confidence
        )

    def get_escalation_stats(self) -> dict:
        """Get statistics about escalation history."""
        if not self.escalation_history:
            return {
                'total_escalations': 0,
                'by_level': {},
                'average_duration': 0
            }

        # Calculate statistics
        level_counts = {}
        for event in self.escalation_history:
            if isinstance(event, dict) and 'level' in event:
                level = event['level']
                level_counts[level] = level_counts.get(level, 0) + 1

        return {
            'total_escalations': len(self.escalation_history),
            'by_level': level_counts,
            'current_level': self.current_level.name,
            'consecutive_misaligned': self.consecutive_misaligned
        }
