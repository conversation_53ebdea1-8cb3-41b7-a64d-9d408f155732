#!/usr/bin/env python3
"""Test the system with misaligned tasks to trigger escalation."""

import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from shared import Config, Task as SharedTask
from src.apis.task_management import Task, TaskManager, TaskStatus
from src.screenshot_taker import ScreenshotCapture, ScreenshotConfig
from src.agents.ai_reasoning import AIReasoning, AIConfig
from src.agents.escalation import EscalationManager
from src.apis.data_privacy import DataStore


def test_misalignment():
    """Test system behavior when user is off-task."""
    print("="*60)
    print("TESTING MISALIGNMENT & ESCALATION")
    print("="*60)
    print()
    
    # Initialize components
    config = Config()
    config.data_directory = Path("/tmp/productivity_guard_test")
    config.data_directory.mkdir(exist_ok=True)
    
    # Create a task manager with a programming task
    task_manager = TaskManager(db_path=":memory:")
    task = Task(
        id="test-001",
        title="Complete code review",
        priority=5,
        description="Review pull requests and provide feedback",
        status="active"
    )
    task_manager.add_task(task)
    
    # Initialize AI reasoning with mock mode
    ai_config = AIConfig(provider="mock", enable_fallback_mock=True)
    ai_reasoning = AIReasoning(config=ai_config)
    
    # Initialize screenshot capture
    screenshot_config = ScreenshotConfig(
        output_directory=str(config.data_directory / "screenshots")
    )
    screenshot_capture = ScreenshotCapture(screenshot_config)
    
    # Initialize escalation manager
    escalation_manager = EscalationManager(
        config={"severe_threshold": 3}
    )
    
    # Initialize data store
    data_store = DataStore(db_path=":memory:")
    
    print("Simulating user browsing social media while supposed to be coding...")
    print()
    
    # Create a social media task to trigger misalignment
    social_media_task = SharedTask(
        id="test-002",
        title="Browse social media",
        priority=1,
        description="Checking Facebook and Twitter feeds",
        status="active"
    )
    
    # Run multiple cycles to trigger escalation
    misaligned_count = 0
    for i in range(5):
        print(f"Cycle {i+1}:")
        
        # Capture screenshot
        screenshot = screenshot_capture.capture()
        print(f"  - Screenshot captured: {screenshot.id}")
        
        # Analyze with the social media context to trigger misalignment
        result = ai_reasoning.analyze_screenshot(screenshot, social_media_task)
        
        print(f"  - Analysis: {'Aligned' if result.is_aligned else 'Not aligned'} "
              f"(confidence: {result.confidence:.2f})")
        print(f"  - Reasoning: {result.reasoning}")
        
        if not result.is_aligned:
            misaligned_count += 1
            
            # Save to data store
            screenshot_id = data_store.save_screenshot(screenshot.image_path)
            analysis_data = {
                "id": result.id,
                "is_aligned": result.is_aligned,
                "confidence": result.confidence,
                "reasoning": result.reasoning
            }
            data_store.save_analysis(
                screenshot_id=screenshot_id,
                analysis_type="task_alignment",
                result_data=analysis_data,
                confidence=result.confidence
            )
            
            # Check escalation
            history = data_store.get_history(
                data_type='analysis_results',
                limit=10
            )
            recent_results = [record['result_data'] for record in history if 'result_data' in record]
            
            escalation_level = escalation_manager.check_escalation_needed(recent_results)
            
            if escalation_level.value > 0:
                print(f"  ⚠️  ESCALATION TRIGGERED: {escalation_level.name}")
                escalation_manager.execute_escalation(escalation_level)
        
        print()
        time.sleep(1)
    
    print("="*60)
    print(f"Summary: {misaligned_count} misalignments detected")
    print("="*60)


if __name__ == "__main__":
    test_misalignment()