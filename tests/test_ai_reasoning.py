#!/usr/bin/env python3
"""Tests for the AI Reasoning module."""

import sys
import time
from datetime import datetime
from pathlib import Path

import pytest

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.agents.ai_reasoning import AIConfig, AIReasoning
from shared.types import AIAnalysisResult, Screenshot, Task


class TestAIReasoning:
    """Test suite for AI Reasoning functionality."""

    @pytest.fixture
    def ai_reasoning(self):
        """Create an AI Reasoning instance for testing."""
        config = AIConfig(
            model_name="mock-gpt-4v-test",
            confidence_threshold_high=0.8,
            confidence_threshold_medium=0.5,
            max_processing_time_ms=3000
        )
        return AIReasoning(config)

    @pytest.fixture
    def sample_task(self):
        """Create a sample task for testing."""
        return Task(
            id="test-task-001",
            title="Complete Python module development",
            description="Implement and test the AI reasoning module with comprehensive test coverage",
            priority=5,
            status="active",
            tags=["development", "python", "testing"]
        )

    @pytest.fixture
    def sample_screenshot(self):
        """Create a sample screenshot for testing."""
        return Screenshot(
            id="test-screenshot-001",
            timestamp=datetime.now(),
            image_path="/tmp/test_screenshot.jpg",
            monitor_id=0,
            file_size=1024000
        )

    def test_initialization(self):
        """Test AI Reasoning initialization."""
        # Test with default config
        ai1 = AIReasoning()
        assert ai1.config.model_name == "mock-gpt-4v"
        assert ai1.config.confidence_threshold_high == 0.8

        # Test with custom config
        custom_config = AIConfig(
            model_name="custom-model",
            confidence_threshold_high=0.9,
            confidence_threshold_medium=0.6
        )
        ai2 = AIReasoning(custom_config)
        assert ai2.config.model_name == "custom-model"
        assert ai2.config.confidence_threshold_high == 0.9

    def test_analyze_screenshot_returns_valid_result(self, ai_reasoning, sample_task, sample_screenshot):
        """Test that analyze_screenshot returns a valid AIAnalysisResult."""
        result = ai_reasoning.analyze_screenshot(sample_screenshot, sample_task)

        # Check result type
        assert isinstance(result, AIAnalysisResult)

        # Check required fields
        assert result.id is not None
        assert result.screenshot_id == sample_screenshot.id
        assert result.task_id == sample_task.id
        assert isinstance(result.is_aligned, bool)
        assert 0.0 <= result.confidence <= 1.0
        assert isinstance(result.reasoning, str)
        assert len(result.reasoning) > 0
        assert isinstance(result.suggestions, list)
        assert result.processing_time_ms > 0
        assert result.processing_time_ms < ai_reasoning.config.max_processing_time_ms

    def test_confidence_ranges(self, ai_reasoning, sample_task, sample_screenshot):
        """Test that confidence scores fall within expected ranges."""
        # Run multiple analyses to test randomness
        confidences = []
        for _ in range(10):
            result = ai_reasoning.analyze_screenshot(sample_screenshot, sample_task)
            confidences.append(result.confidence)

        # All confidences should be between 0 and 1
        assert all(0.0 <= c <= 1.0 for c in confidences)

        # Should have some variation
        assert len(set(confidences)) > 1

    def test_should_prompt_user_high_confidence_misalignment(self, ai_reasoning):
        """Test that high confidence misalignment triggers user prompt."""
        # Create a misaligned result with high confidence
        result = AIAnalysisResult(
            id="test-001",
            screenshot_id="screenshot-001",
            task_id="task-001",
            is_aligned=False,
            confidence=0.85,
            reasoning="User is on social media",
            suggestions=["Close social media", "Return to work"]
        )

        assert ai_reasoning.should_prompt_user(result) is True

    def test_should_prompt_user_medium_confidence_misalignment(self, ai_reasoning):
        """Test that medium confidence misalignment triggers user prompt."""
        result = AIAnalysisResult(
            id="test-002",
            screenshot_id="screenshot-002",
            task_id="task-002",
            is_aligned=False,
            confidence=0.65,
            reasoning="Possibly distracted",
            suggestions=["Check if this is task-related"]
        )

        assert ai_reasoning.should_prompt_user(result) is True

    def test_should_not_prompt_user_low_confidence(self, ai_reasoning):
        """Test that low confidence doesn't trigger user prompt."""
        result = AIAnalysisResult(
            id="test-003",
            screenshot_id="screenshot-003",
            task_id="task-003",
            is_aligned=False,
            confidence=0.45,
            reasoning="Uncertain about activity",
            suggestions=[]
        )

        assert ai_reasoning.should_prompt_user(result) is False

    def test_should_not_prompt_user_aligned(self, ai_reasoning):
        """Test that aligned activity doesn't trigger user prompt."""
        result = AIAnalysisResult(
            id="test-004",
            screenshot_id="screenshot-004",
            task_id="task-004",
            is_aligned=True,
            confidence=0.90,
            reasoning="User is actively coding",
            suggestions=[]
        )

        assert ai_reasoning.should_prompt_user(result) is False

    def test_get_model_info(self, ai_reasoning):
        """Test get_model_info returns expected structure."""
        info = ai_reasoning.get_model_info()

        # Check required fields
        assert "model_name" in info
        assert "model_type" in info
        assert "status" in info
        assert "confidence_thresholds" in info
        assert "performance" in info
        assert "capabilities" in info
        assert "last_updated" in info

        # Check nested structures
        assert "high" in info["confidence_thresholds"]
        assert "medium" in info["confidence_thresholds"]
        assert info["confidence_thresholds"]["high"] == 0.8
        assert info["confidence_thresholds"]["medium"] == 0.5

        # Check capabilities
        capabilities = info["capabilities"]
        assert "screenshot_analysis" in capabilities
        assert "task_alignment_detection" in capabilities
        assert "confidence_scoring" in capabilities
        assert "suggestion_generation" in capabilities

    def test_update_model_feedback(self, ai_reasoning):
        """Test that model feedback is accepted without errors."""
        # Should not raise any exceptions
        ai_reasoning.update_model_feedback("analysis-001", was_accurate=True)
        ai_reasoning.update_model_feedback("analysis-002", was_accurate=False)

    def test_processing_time_within_limits(self, ai_reasoning, sample_task, sample_screenshot):
        """Test that processing time stays within configured limits."""
        start_time = time.time()
        result = ai_reasoning.analyze_screenshot(sample_screenshot, sample_task)
        actual_time_ms = (time.time() - start_time) * 1000

        # Check that reported time is reasonable
        assert result.processing_time_ms <= actual_time_ms + 100  # Allow 100ms margin
        assert result.processing_time_ms <= ai_reasoning.config.max_processing_time_ms

    def test_different_task_types_produce_different_results(self, ai_reasoning, sample_screenshot):
        """Test that different task types can produce different analysis results."""
        # Programming task
        prog_task = Task(
            id="prog-001",
            title="Code development",
            description="Develop new features in Python",
            priority=5,
            status="active"
        )

        # Social media task (should be more likely to flag as misaligned)
        social_task = Task(
            id="social-001",
            title="Social media management",
            description="Post updates on social media platforms",
            priority=2,
            status="active"
        )

        # Run multiple analyses
        prog_results = [ai_reasoning.analyze_screenshot(sample_screenshot, prog_task) for _ in range(5)]
        social_results = [ai_reasoning.analyze_screenshot(sample_screenshot, social_task) for _ in range(5)]

        # Should have some variety in results
        prog_alignments = [r.is_aligned for r in prog_results]
        social_alignments = [r.is_aligned for r in social_results]

        # At least one result should be different (tests randomness)
        assert len(set(prog_alignments + social_alignments)) > 1

    def test_suggestions_provided_when_needed(self, ai_reasoning, sample_task, sample_screenshot):
        """Test that suggestions are provided for misaligned or low confidence scenarios."""
        # Run multiple analyses to get different scenarios
        for _ in range(10):
            result = ai_reasoning.analyze_screenshot(sample_screenshot, sample_task)

            # If misaligned, should have suggestions
            if not result.is_aligned:
                assert len(result.suggestions) > 0
                assert all(isinstance(s, str) and len(s) > 0 for s in result.suggestions)

            # If low confidence, might have suggestions
            if result.confidence < 0.7 and not result.is_aligned:
                assert len(result.suggestions) > 0

    def test_reasoning_contains_task_context(self, ai_reasoning, sample_task, sample_screenshot):
        """Test that reasoning mentions the task being analyzed."""
        result = ai_reasoning.analyze_screenshot(sample_screenshot, sample_task)

        # Reasoning should reference the task title
        assert sample_task.title in result.reasoning or "task" in result.reasoning.lower()

        # Reasoning should be meaningful
        assert len(result.reasoning) > 20
        assert result.reasoning != ""


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
