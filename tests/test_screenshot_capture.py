"""Tests for screenshot capture functionality."""

import os
import shutil
import sys
import tempfile
import time
import unittest
from unittest.mock import MagicMock, patch

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.screenshot_taker import ScreenshotCapture, ScreenshotConfig


class TestScreenshotConfig(unittest.TestCase):
    """Tests for ScreenshotConfig dataclass."""

    def test_default_config(self):
        """Test default configuration values."""
        config = ScreenshotConfig()
        self.assertEqual(config.capture_interval, 1.0)
        self.assertEqual(config.output_directory, "./screenshots")
        self.assertEqual(config.image_format, "png")
        self.assertEqual(config.quality, 95)
        self.assertIsNone(config.monitor_index)
        self.assertIsNone(config.capture_region)
        self.assertEqual(config.filename_prefix, "screenshot")
        self.assertTrue(config.include_timestamp)

    def test_custom_config(self):
        """Test custom configuration values."""
        config = ScreenshotConfig(
            capture_interval=2.5,
            output_directory="/tmp/screenshots",
            image_format="jpg",
            quality=80,
            monitor_index=1,
            capture_region=(100, 100, 800, 600),
            filename_prefix="test",
            include_timestamp=False
        )
        self.assertEqual(config.capture_interval, 2.5)
        self.assertEqual(config.output_directory, "/tmp/screenshots")
        self.assertEqual(config.image_format, "jpg")
        self.assertEqual(config.quality, 80)
        self.assertEqual(config.monitor_index, 1)
        self.assertEqual(config.capture_region, (100, 100, 800, 600))
        self.assertEqual(config.filename_prefix, "test")
        self.assertFalse(config.include_timestamp)


class TestScreenshotCapture(unittest.TestCase):
    """Tests for ScreenshotCapture class."""

    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config = ScreenshotConfig(output_directory=self.temp_dir)
        self.capture = ScreenshotCapture(self.config)

    def tearDown(self):
        """Clean up test environment."""
        if hasattr(self, 'capture') and self.capture.is_capturing():
            self.capture.stop_capture_loop()
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_initialization(self):
        """Test ScreenshotCapture initialization."""
        self.assertEqual(self.capture.config.output_directory, self.temp_dir)
        self.assertFalse(self.capture.is_capturing())
        self.assertTrue(os.path.exists(self.temp_dir))

    def test_initialization_creates_directory(self):
        """Test that initialization creates output directory."""
        new_dir = os.path.join(self.temp_dir, "new_screenshots")
        config = ScreenshotConfig(output_directory=new_dir)
        capture = ScreenshotCapture(config)
        self.assertTrue(os.path.exists(new_dir))

    @patch('mss.mss')
    def test_capture_single_screenshot(self, mock_mss):
        """Test capturing a single screenshot."""
        # Mock the mss context manager
        mock_sct = MagicMock()
        mock_mss.return_value.__enter__.return_value = mock_sct

        # Mock screenshot data
        mock_screenshot = MagicMock()
        mock_screenshot.size = (1920, 1080)
        mock_screenshot.bgra = b'\x00' * (1920 * 1080 * 4)
        mock_sct.grab.return_value = mock_screenshot
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 1920, "height": 1080}
        ]

        # Capture screenshot
        filepath = self.capture.capture()

        # Verify
        self.assertTrue(os.path.exists(filepath))
        self.assertTrue(filepath.startswith(self.temp_dir))
        self.assertTrue(filepath.endswith('.png'))

    @patch('mss.mss')
    def test_capture_with_custom_filename(self, mock_mss):
        """Test capturing with custom filename."""
        # Mock the mss context manager
        mock_sct = MagicMock()
        mock_mss.return_value.__enter__.return_value = mock_sct

        # Mock screenshot data
        mock_screenshot = MagicMock()
        mock_screenshot.size = (1920, 1080)
        mock_screenshot.bgra = b'\x00' * (1920 * 1080 * 4)
        mock_sct.grab.return_value = mock_screenshot
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 1920, "height": 1080}
        ]

        # Capture with custom filename
        custom_filename = "custom_screenshot.png"
        filepath = self.capture.capture(custom_filename)

        # Verify
        self.assertEqual(filepath, os.path.join(self.temp_dir, custom_filename))
        self.assertTrue(os.path.exists(filepath))

    @patch('mss.mss')
    def test_capture_specific_monitor(self, mock_mss):
        """Test capturing specific monitor."""
        # Mock the mss context manager
        mock_sct = MagicMock()
        mock_mss.return_value.__enter__.return_value = mock_sct

        # Mock monitors
        mock_sct.monitors = [
            {"top": 0, "left": 0, "width": 1920, "height": 1080},
            {"top": 0, "left": 1920, "width": 1920, "height": 1080}
        ]

        # Mock screenshot data
        mock_screenshot = MagicMock()
        mock_screenshot.size = (1920, 1080)
        mock_screenshot.bgra = b'\x00' * (1920 * 1080 * 4)
        mock_sct.grab.return_value = mock_screenshot

        # Test with monitor index 1
        config = ScreenshotConfig(
            output_directory=self.temp_dir,
            monitor_index=1
        )
        capture = ScreenshotCapture(config)
        filepath = capture.capture()

        # Verify correct monitor was used
        mock_sct.grab.assert_called_with(mock_sct.monitors[1])
        self.assertTrue(os.path.exists(filepath))

    @patch('mss.mss')
    def test_capture_region(self, mock_mss):
        """Test capturing specific region."""
        # Mock the mss context manager
        mock_sct = MagicMock()
        mock_mss.return_value.__enter__.return_value = mock_sct

        # Mock screenshot data
        mock_screenshot = MagicMock()
        mock_screenshot.size = (800, 600)
        mock_screenshot.bgra = b'\x00' * (800 * 600 * 4)
        mock_sct.grab.return_value = mock_screenshot

        # Test with specific region
        region = (100, 200, 800, 600)
        config = ScreenshotConfig(
            output_directory=self.temp_dir,
            capture_region=region
        )
        capture = ScreenshotCapture(config)
        filepath = capture.capture()

        # Verify correct region was used
        expected_monitor = {
            "top": 200,
            "left": 100,
            "width": 800,
            "height": 600
        }
        mock_sct.grab.assert_called_with(expected_monitor)
        self.assertTrue(os.path.exists(filepath))

    @patch('mss.mss')
    def test_invalid_monitor_index(self, mock_mss):
        """Test error handling for invalid monitor index."""
        # Mock the mss context manager
        mock_sct = MagicMock()
        mock_mss.return_value.__enter__.return_value = mock_sct
        mock_sct.monitors = [{"top": 0, "left": 0, "width": 1920, "height": 1080}]

        # Test with invalid monitor index
        config = ScreenshotConfig(
            output_directory=self.temp_dir,
            monitor_index=99
        )
        capture = ScreenshotCapture(config)

        with self.assertRaises(ValueError):
            capture.capture()

    @patch('mss.mss')
    def test_get_monitors(self, mock_mss):
        """Test getting list of monitors."""
        # Mock the mss context manager
        mock_sct = MagicMock()
        mock_mss.return_value.__enter__.return_value = mock_sct

        expected_monitors = [
            {"top": 0, "left": 0, "width": 1920, "height": 1080},
            {"top": 0, "left": 1920, "width": 1920, "height": 1080}
        ]
        mock_sct.monitors = expected_monitors

        monitors = self.capture.get_monitors()
        self.assertEqual(monitors, expected_monitors)

    def test_filename_generation_with_timestamp(self):
        """Test filename generation with timestamp."""
        config = ScreenshotConfig(
            output_directory=self.temp_dir,
            filename_prefix="test",
            include_timestamp=True,
            image_format="png"
        )
        capture = ScreenshotCapture(config)

        filename = capture._generate_filename()
        self.assertTrue(filename.startswith("test_"))
        self.assertTrue(filename.endswith(".png"))
        # Check timestamp format (basic check)
        self.assertGreater(len(filename), len("test_.png"))

    def test_filename_generation_without_timestamp(self):
        """Test filename generation without timestamp."""
        config = ScreenshotConfig(
            output_directory=self.temp_dir,
            filename_prefix="static",
            include_timestamp=False,
            image_format="jpg"
        )
        capture = ScreenshotCapture(config)

        filename = capture._generate_filename()
        self.assertEqual(filename, "static.jpg")

    @patch('screenshot_capture.capture.ScreenshotCapture.capture')
    def test_capture_loop_start_stop(self, mock_capture):
        """Test starting and stopping capture loop."""
        # Start capture loop
        self.assertFalse(self.capture.is_capturing())
        self.capture.start_capture_loop()
        self.assertTrue(self.capture.is_capturing())

        # Let it run briefly
        time.sleep(0.1)

        # Stop capture loop
        self.capture.stop_capture_loop()
        self.assertFalse(self.capture.is_capturing())

        # Verify capture was called
        self.assertGreater(mock_capture.call_count, 0)

    def test_capture_loop_already_running(self):
        """Test error when trying to start capture loop that's already running."""
        self.capture.start_capture_loop()

        with self.assertRaises(RuntimeError):
            self.capture.start_capture_loop()

        self.capture.stop_capture_loop()

    def test_stop_capture_loop_when_not_running(self):
        """Test stopping capture loop when it's not running (should not error)."""
        self.assertFalse(self.capture.is_capturing())
        self.capture.stop_capture_loop()  # Should not raise any error
        self.assertFalse(self.capture.is_capturing())

    @patch('mss.mss')
    def test_jpeg_quality_setting(self, mock_mss):
        """Test JPEG quality setting."""
        # Mock the mss context manager
        mock_sct = MagicMock()
        mock_mss.return_value.__enter__.return_value = mock_sct

        # Mock screenshot data
        mock_screenshot = MagicMock()
        mock_screenshot.size = (100, 100)
        mock_screenshot.bgra = b'\x00' * (100 * 100 * 4)
        mock_sct.grab.return_value = mock_screenshot
        mock_sct.monitors = [{"top": 0, "left": 0, "width": 100, "height": 100}]

        # Test JPEG with custom quality
        config = ScreenshotConfig(
            output_directory=self.temp_dir,
            image_format="jpg",
            quality=50
        )
        capture = ScreenshotCapture(config)
        filepath = capture.capture()

        self.assertTrue(filepath.endswith('.jpg'))
        self.assertTrue(os.path.exists(filepath))


if __name__ == '__main__':
    unittest.main()
