"""
Unit tests for the Data & Privacy module
"""

import json
import os
import sys
import tempfile
import unittest
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from PIL import Image

from src.apis.data_privacy import DataStore, PrivacyManager, SensitiveDataType, SensitivityLevel


class TestDataStore(unittest.TestCase):
    """Test cases for DataStore class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.db_path = self.temp_db.name
        self.temp_db.close()

        # Create test image
        self.test_image = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        self.test_image_path = self.test_image.name
        self.test_image.close()

        # Create a simple test image
        img = Image.new('RGB', (100, 100), color='red')
        img.save(self.test_image_path)

    def tearDown(self):
        """Clean up test fixtures."""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
        if os.path.exists(self.test_image_path):
            os.unlink(self.test_image_path)

    def test_initialization(self):
        """Test DataStore initialization."""
        store = DataStore(self.db_path)
        self.assertTrue(os.path.exists(self.db_path))

        # Test with encryption
        store_encrypted = DataStore(self.db_path + '_enc', password='test123')
        self.assertIsNotNone(store_encrypted.cipher_suite)
        os.unlink(self.db_path + '_enc')

    def test_save_screenshot(self):
        """Test saving screenshots."""
        store = DataStore(self.db_path)

        # Save without metadata
        screenshot_id = store.save_screenshot(self.test_image_path)
        self.assertIsInstance(screenshot_id, int)
        self.assertGreater(screenshot_id, 0)

        # Save with metadata
        screenshot_id2 = store.save_screenshot(
            self.test_image_path,
            tags=['test', 'unit_test'],
            metadata={'test_key': 'test_value'}
        )
        self.assertGreater(screenshot_id2, screenshot_id)

    def test_save_analysis(self):
        """Test saving analysis results."""
        store = DataStore(self.db_path)

        # First save a screenshot
        screenshot_id = store.save_screenshot(self.test_image_path)

        # Save analysis
        analysis_id = store.save_analysis(
            screenshot_id,
            'test_analysis',
            {'result': 'test_data', 'score': 0.95},
            confidence=0.95,
            sensitive_data_detected=True
        )
        self.assertIsInstance(analysis_id, int)
        self.assertGreater(analysis_id, 0)

    def test_get_history(self):
        """Test retrieving history."""
        store = DataStore(self.db_path)

        # Save some data
        screenshot_ids = []
        for i in range(5):
            sid = store.save_screenshot(self.test_image_path, tags=[f'test_{i}'])
            screenshot_ids.append(sid)

        # Get history
        history = store.get_history('screenshots', limit=3)
        self.assertEqual(len(history), 3)

        # Test with date filtering
        history_filtered = store.get_history(
            'screenshots',
            start_date=datetime.now() - timedelta(days=1),
            end_date=datetime.now() + timedelta(days=1)
        )
        self.assertEqual(len(history_filtered), 5)

    def test_redact_sensitive_data(self):
        """Test screenshot redaction."""
        store = DataStore(self.db_path)

        # Save a screenshot
        screenshot_id = store.save_screenshot(self.test_image_path)

        # Redact regions
        regions = [(10, 10, 20, 20), (50, 50, 30, 30)]
        success = store.redact_sensitive_data(screenshot_id, regions)
        self.assertTrue(success)

        # Verify redaction was marked
        history = store.get_history('screenshots', limit=1)
        self.assertTrue(history[0]['redacted'])

    def test_encryption(self):
        """Test data encryption functionality."""
        password = 'test_password_123'
        store = DataStore(self.db_path, password=password)

        # Save encrypted data
        screenshot_id = store.save_screenshot(self.test_image_path)
        analysis_id = store.save_analysis(
            screenshot_id,
            'encrypted_test',
            {'secret': 'encrypted_data'}
        )

        # Verify data is encrypted in database
        import sqlite3
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT result_data FROM analysis_results WHERE id = ?', (analysis_id,))
        raw_data = cursor.fetchone()[0]
        conn.close()

        # Raw data should not contain the plaintext
        self.assertNotIn('encrypted_data', raw_data)

        # But should be decrypted when retrieved through the store
        history = store.get_history('analysis_results', limit=1)
        self.assertEqual(history[0]['result_data']['secret'], 'encrypted_data')

    def test_retention_policies(self):
        """Test retention policy enforcement."""
        store = DataStore(self.db_path)

        # The default policies should be set
        stats_before = store.get_storage_stats()

        # Enforce policies (should not delete anything new)
        deleted = store.enforce_retention_policies()
        self.assertIsInstance(deleted, dict)

    def test_export_data(self):
        """Test data export functionality."""
        store = DataStore(self.db_path)

        # Save some data
        screenshot_id = store.save_screenshot(self.test_image_path)
        store.save_analysis(screenshot_id, 'test', {'data': 'test'})

        # Export data
        export_path = self.db_path + '_export.json'
        success = store.export_data(export_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(export_path))

        # Verify export content
        with open(export_path) as f:
            exported = json.load(f)

        self.assertIn('screenshots', exported)
        self.assertIn('analysis_results', exported)

        # Clean up
        os.unlink(export_path)

    def test_storage_stats(self):
        """Test storage statistics."""
        store = DataStore(self.db_path)

        stats = store.get_storage_stats()
        self.assertIn('database_size', stats)
        self.assertIn('table_counts', stats)
        self.assertIsInstance(stats['database_size'], int)
        self.assertIsInstance(stats['table_counts'], dict)


class TestPrivacyManager(unittest.TestCase):
    """Test cases for PrivacyManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.manager = PrivacyManager(SensitivityLevel.MEDIUM)

    def test_initialization(self):
        """Test PrivacyManager initialization."""
        self.assertEqual(self.manager.sensitivity_level, SensitivityLevel.MEDIUM)
        self.assertIsInstance(self.manager.patterns, dict)
        self.assertGreater(len(self.manager.patterns), 0)

    def test_detect_ssn(self):
        """Test SSN detection."""
        test_cases = [
            ("My SSN is ***********", True),
            ("SSN: 123 45 6789", True),
            ("123456789", True),  # May match depending on context
            ("My phone is ***********", True),  # Will match SSN pattern
            ("No sensitive data here", False)
        ]

        for text, should_match in test_cases:
            matches = self.manager.detect_sensitive_data(text)
            if should_match:
                self.assertGreater(len(matches), 0, f"Should find match in: {text}")
                # Check if SSN was detected
                ssn_found = any(m.type == SensitiveDataType.SSN for m in matches)
                if 'SSN' in text:
                    self.assertTrue(ssn_found, f"Should detect SSN in: {text}")
            else:
                self.assertEqual(len(matches), 0, f"Should not find match in: {text}")

    def test_detect_credit_card(self):
        """Test credit card detection."""
        test_cases = [
            ("Card: 4111-1111-1111-1111", True),
            ("4111 1111 1111 1111", True),
            ("****************", True),
            ("Not a card number", False)
        ]

        for text, should_match in test_cases:
            matches = self.manager.detect_sensitive_data(text)
            if should_match:
                self.assertGreater(len(matches), 0, f"Should find match in: {text}")

    def test_detect_email(self):
        """Test email detection."""
        test_cases = [
            ("Email: <EMAIL>", True),
            ("Contact <EMAIL>", True),
            ("Not an email", False)
        ]

        for text, should_match in test_cases:
            matches = self.manager.detect_sensitive_data(text)
            if should_match:
                self.assertGreater(len(matches), 0, f"Should find match in: {text}")
                email_found = any(m.type == SensitiveDataType.EMAIL for m in matches)
                self.assertTrue(email_found)

    def test_detect_api_key(self):
        """Test API key detection."""
        test_cases = [
            ('api_key: "sk_test_4eC39HqLyjWDarjtT1zdp7dc"', True),
            ('API_KEY=abcdef1234567890abcdef1234567890', True),
            ('secret: "my_super_secret_key_12345678901234567890"', True),
            ("Short text", False)
        ]

        for text, should_match in test_cases:
            matches = self.manager.detect_sensitive_data(text)
            if should_match:
                self.assertGreater(len(matches), 0, f"Should find match in: {text}")

    def test_redact_content(self):
        """Test content redaction."""
        content = "My <NAME_EMAIL> and SSN is ***********"
        matches = self.manager.detect_sensitive_data(content)

        redacted = self.manager.redact_content(content, matches)

        # Verify redaction
        self.assertNotIn("<EMAIL>", redacted)
        self.assertNotIn("***********", redacted)
        self.assertIn("[EMAIL-REDACTED]", redacted)
        self.assertIn("[SSN-REDACTED]", redacted)

    def test_custom_patterns(self):
        """Test custom pattern functionality."""
        # Add custom pattern
        self.manager.add_custom_pattern(r'EMP-\d{6}', 'employee_id')

        # Test detection
        content = "Employee ID: EMP-123456"
        matches = self.manager.detect_sensitive_data(content)

        self.assertEqual(len(matches), 1)
        self.assertEqual(matches[0].type, SensitiveDataType.CUSTOM)
        self.assertEqual(matches[0].pattern, "EMP-123456")

    def test_sensitivity_levels(self):
        """Test different sensitivity levels."""
        content = "Possible SSN: 123456789"  # Ambiguous pattern

        # Test with different levels
        for level in [SensitivityLevel.LOW, SensitivityLevel.MEDIUM,
                     SensitivityLevel.HIGH, SensitivityLevel.PARANOID]:
            self.manager.configure_sensitivity_level(level)
            matches = self.manager.detect_sensitive_data(content)

            # Higher sensitivity should detect more
            if level == SensitivityLevel.PARANOID:
                self.assertGreater(len(matches), 0)

    def test_export_import_rules(self):
        """Test rule export and import."""
        # Add custom pattern
        self.manager.add_custom_pattern(r'PROJ-\d{4}', 'project_id')

        # Export rules
        exported = self.manager.export_redaction_rules()

        self.assertIn('sensitivity_level', exported)
        self.assertIn('custom_patterns', exported)
        self.assertIn('project_id', exported['custom_patterns'])

        # Create new manager and import
        new_manager = PrivacyManager()
        new_manager.import_redaction_rules(exported)

        # Verify import
        self.assertEqual(new_manager.sensitivity_level.value,
                        self.manager.sensitivity_level.value)
        self.assertIn('project_id', new_manager.custom_patterns)

    def test_privacy_report(self):
        """Test privacy report generation."""
        # Perform some operations
        content = "Email: <EMAIL>"
        matches = self.manager.detect_sensitive_data(content)
        self.manager.redact_content(content, matches)

        # Generate report
        report = self.manager.generate_privacy_report()

        self.assertIn('report_date', report)
        self.assertIn('sensitivity_level', report)
        self.assertIn('redaction_summary', report)
        self.assertGreater(report['redaction_summary']['total_redactions'], 0)

    def test_analyze_image_regions(self):
        """Test image region analysis."""
        regions = [
            {'text': 'Email: <EMAIL>', 'bbox': [10, 10, 100, 20]},
            {'text': 'Normal text', 'bbox': [10, 40, 100, 20]},
            {'text': 'SSN: ***********', 'bbox': [10, 70, 100, 20]}
        ]

        sensitive_regions = self.manager.analyze_image_regions(regions)

        self.assertEqual(len(sensitive_regions), 2)  # Email and SSN regions

        for region in sensitive_regions:
            self.assertIn('bbox', region)
            self.assertIn('matches', region)
            self.assertIn('redacted_text', region)
            self.assertNotEqual(region['original_text'], region['redacted_text'])


class TestIntegration(unittest.TestCase):
    """Integration tests for DataStore and PrivacyManager."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.db_path = self.temp_db.name
        self.temp_db.close()

        self.store = DataStore(self.db_path, password='test_password')
        self.manager = PrivacyManager(SensitivityLevel.HIGH)

        # Create test image
        self.test_image = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        self.test_image_path = self.test_image.name
        self.test_image.close()

        img = Image.new('RGB', (200, 200), color='white')
        img.save(self.test_image_path)

    def tearDown(self):
        """Clean up test fixtures."""
        if os.path.exists(self.db_path):
            os.unlink(self.db_path)
        if os.path.exists(self.test_image_path):
            os.unlink(self.test_image_path)

    def test_privacy_aware_storage(self):
        """Test storing data with privacy detection."""
        # Save screenshot
        screenshot_id = self.store.save_screenshot(self.test_image_path)

        # Simulate OCR result with sensitive data
        ocr_text = "User: John Doe, Email: <EMAIL>, SSN: ***********"

        # Detect sensitive data
        matches = self.manager.detect_sensitive_data(ocr_text)
        self.assertGreater(len(matches), 0)

        # Save analysis with privacy information
        analysis_id = self.store.save_analysis(
            screenshot_id,
            'ocr_with_privacy',
            {
                'original_text': ocr_text,
                'redacted_text': self.manager.redact_content(ocr_text, matches),
                'sensitive_data_types': list(set(m.type.value for m in matches))
            },
            sensitive_data_detected=True
        )

        # Retrieve and verify
        history = self.store.get_history('analysis_results', limit=1)
        self.assertTrue(history[0]['sensitive_data_detected'])
        self.assertIn('redacted_text', history[0]['result_data'])
        self.assertNotIn('***********', history[0]['result_data']['redacted_text'])


if __name__ == '__main__':
    unittest.main()
