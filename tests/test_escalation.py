"""
Tests for the Escalation Mode feature.
"""

import sys
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import patch

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from src.agents.escalation.escalation_manager import EscalationManager
from src.agents.escalation.notification_handler import NotificationHandler
from shared.types import AIAnalysisResult, EscalationLevel


class TestEscalationManager(unittest.TestCase):
    """Test cases for EscalationManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'mild_threshold': 3,
            'moderate_threshold': 6,
            'severe_threshold': 10,
            'aligned_for_deescalation': 3,
            'deescalation_cooldown_minutes': 5,
            'low_confidence_threshold': 0.7,
            'break_hours': [12, 13],
            'working_hours': (9, 18)
        }
        self.manager = EscalationManager(self.config)
        self.task_id = "test-task-001"

    def create_analysis_result(self, is_aligned: bool, confidence: float = 0.9) -> AIAnalysisResult:
        """Helper to create analysis results."""
        return AIAnalysisResult(
            id=f"analysis-{datetime.now().timestamp()}",
            screenshot_id=f"screenshot-{datetime.now().timestamp()}",
            task_id=self.task_id,
            is_aligned=is_aligned,
            confidence=confidence,
            reasoning="Test reasoning",
            suggestions=["Test suggestion"]
        )

    def test_initial_state(self):
        """Test initial escalation state."""
        self.assertEqual(self.manager.get_current_level(), EscalationLevel.NONE)
        self.assertEqual(self.manager.consecutive_misaligned, 0)
        self.assertEqual(self.manager.consecutive_aligned, 0)

    def test_no_escalation_when_aligned(self):
        """Test that no escalation occurs when user is aligned."""
        history = [self.create_analysis_result(is_aligned=True) for _ in range(10)]
        level = self.manager.check_escalation_needed(history)
        self.assertEqual(level, EscalationLevel.NONE)
        self.assertEqual(self.manager.consecutive_misaligned, 0)

    def test_mild_escalation_threshold(self):
        """Test escalation to MILD level."""
        history = []

        # Add 2 misaligned - should still be NONE
        for _ in range(2):
            history.insert(0, self.create_analysis_result(is_aligned=False))
        level = self.manager.check_escalation_needed(history)
        self.assertEqual(level, EscalationLevel.NONE)

        # Add 1 more (total 3) - should escalate to MILD
        history.insert(0, self.create_analysis_result(is_aligned=False))
        level = self.manager.check_escalation_needed(history)
        self.assertEqual(level, EscalationLevel.MILD)

    def test_moderate_escalation_threshold(self):
        """Test escalation to MODERATE level."""
        history = []

        # Add 6 misaligned results
        for _ in range(6):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            self.manager.check_escalation_needed(history)

        level = self.manager.get_current_level()
        self.assertEqual(level, EscalationLevel.MODERATE)

    def test_severe_escalation_threshold(self):
        """Test escalation to SEVERE level."""
        history = []

        # Add 10 misaligned results
        for _ in range(10):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            self.manager.check_escalation_needed(history)

        level = self.manager.get_current_level()
        self.assertEqual(level, EscalationLevel.SEVERE)

    def test_low_confidence_handling(self):
        """Test that low confidence results are weighted less."""
        history = []

        # Add 3 low confidence misaligned results
        for _ in range(3):
            history.insert(0, self.create_analysis_result(is_aligned=False, confidence=0.5))
            self.manager.check_escalation_needed(history)

        # Should not escalate yet (3 * 0.5 = 1.5 < 3)
        self.assertEqual(self.manager.get_current_level(), EscalationLevel.NONE)

        # Add 3 more low confidence
        for _ in range(3):
            history.insert(0, self.create_analysis_result(is_aligned=False, confidence=0.5))
            self.manager.check_escalation_needed(history)

        # Now should escalate (6 * 0.5 = 3)
        self.assertEqual(self.manager.get_current_level(), EscalationLevel.MILD)

    def test_deescalation(self):
        """Test de-escalation when user returns to task."""
        # First escalate to MODERATE
        history = []
        for _ in range(6):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            self.manager.check_escalation_needed(history)

        self.assertEqual(self.manager.get_current_level(), EscalationLevel.MODERATE)

        # Now add aligned results
        for _ in range(3):
            history.insert(0, self.create_analysis_result(is_aligned=True))
            level = self.manager.check_escalation_needed(history)

        # Should de-escalate
        self.assertLess(level.value, EscalationLevel.MODERATE.value)

    @patch('escalation.escalation_manager.datetime')
    def test_break_time_no_escalation(self, mock_datetime):
        """Test that no escalation occurs during break time."""
        # Set current time to break hour (12 PM)
        mock_now = datetime(2024, 1, 1, 12, 30)
        mock_datetime.now.return_value = mock_now

        history = []
        for _ in range(10):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            level = self.manager.check_escalation_needed(history)

        # Should remain at NONE during break
        self.assertEqual(level, EscalationLevel.NONE)

    @patch('escalation.escalation_manager.datetime')
    def test_outside_working_hours(self, mock_datetime):
        """Test that no escalation occurs outside working hours."""
        # Set current time to 8 PM (outside 9-6)
        mock_now = datetime(2024, 1, 1, 20, 0)
        mock_datetime.now.return_value = mock_now

        history = []
        for _ in range(10):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            level = self.manager.check_escalation_needed(history)

        # Should remain at NONE outside working hours
        self.assertEqual(level, EscalationLevel.NONE)

    def test_reset_escalation(self):
        """Test reset_escalation method."""
        # First escalate
        history = []
        for _ in range(6):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            self.manager.check_escalation_needed(history)

        self.assertEqual(self.manager.get_current_level(), EscalationLevel.MODERATE)

        # Reset
        self.manager.reset_escalation()

        # Check reset state
        self.assertEqual(self.manager.get_current_level(), EscalationLevel.NONE)
        self.assertEqual(self.manager.consecutive_misaligned, 0)
        self.assertEqual(self.manager.consecutive_aligned, 0)
        self.assertIsNone(self.manager.last_escalation_time)

    def test_configure_thresholds(self):
        """Test dynamic threshold configuration."""
        new_config = {
            'mild_threshold': 2,
            'moderate_threshold': 4,
            'severe_threshold': 6
        }
        self.manager.configure_thresholds(new_config)

        # Test new thresholds
        history = []
        for _ in range(2):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            self.manager.check_escalation_needed(history)

        # Should escalate at 2 instead of 3
        self.assertEqual(self.manager.get_current_level(), EscalationLevel.MILD)

    @patch('escalation.escalation_manager.NotificationHandler')
    def test_execute_escalation_mild(self, mock_notification_handler):
        """Test execution of MILD escalation."""
        task_context = {'title': 'Test Task'}
        self.manager.execute_escalation(EscalationLevel.MILD, task_context)

        # Should send notification
        self.manager.notification_handler.send_notification.assert_called_once()
        call_args = self.manager.notification_handler.send_notification.call_args[1]
        self.assertEqual(call_args['urgency'], 'low')
        self.assertIn('Test Task', call_args['message'])

    @patch('escalation.escalation_manager.NotificationHandler')
    def test_execute_escalation_moderate(self, mock_notification_handler):
        """Test execution of MODERATE escalation."""
        self.manager.consecutive_misaligned = 6
        task_context = {'title': 'Important Task'}
        self.manager.execute_escalation(EscalationLevel.MODERATE, task_context)

        # Should send notification with acknowledgment required
        self.manager.notification_handler.send_notification.assert_called_once()
        call_args = self.manager.notification_handler.send_notification.call_args[1]
        self.assertEqual(call_args['urgency'], 'medium')
        self.assertTrue(call_args['require_acknowledgment'])

    def test_escalation_stats(self):
        """Test getting escalation statistics."""
        # Create some escalation events
        history = []
        for _ in range(6):
            history.insert(0, self.create_analysis_result(is_aligned=False))
            self.manager.check_escalation_needed(history)
            self.manager.execute_escalation(self.manager.get_current_level())

        stats = self.manager.get_escalation_stats()
        self.assertIn('total_escalations', stats)
        self.assertIn('current_level', stats)
        self.assertEqual(stats['current_level'], 'MODERATE')


class TestNotificationHandler(unittest.TestCase):
    """Test cases for NotificationHandler class."""

    def setUp(self):
        """Set up test fixtures."""
        self.handler = NotificationHandler()

    @patch('escalation.notification_handler.notification')
    def test_send_notification_with_plyer(self, mock_notification):
        """Test sending notification with plyer."""
        # Mock plyer availability
        with patch('escalation.notification_handler.PLYER_AVAILABLE', True):
            result = self.handler.send_notification(
                title="Test",
                message="Test message",
                urgency="low"
            )

            # Should succeed
            self.assertTrue(result)

    def test_send_notification_fallback(self):
        """Test fallback notification method."""
        # Mock all notification methods as unavailable
        with patch('escalation.notification_handler.PLYER_AVAILABLE', False), \
             patch('escalation.notification_handler.MACOS_NATIVE', False), \
             patch('escalation.notification_handler.WIN_TOAST', False), \
             patch('escalation.notification_handler.LINUX_NOTIFY', False):

            # Should still succeed with fallback
            result = self.handler.send_notification(
                title="Test",
                message="Test message",
                urgency="high"
            )
            self.assertTrue(result)

    def test_platform_specific_methods(self):
        """Test that platform-specific methods are called correctly."""
        # Test macOS
        if self.handler.platform == "Darwin":
            with patch('subprocess.run') as mock_run:
                self.handler._send_macos_notification("Title", "Message", True, "high")
                mock_run.assert_called_once()

        # Test Windows
        elif self.handler.platform == "Windows":
            if hasattr(self.handler, 'win_toaster'):
                with patch.object(self.handler.win_toaster, 'show_toast') as mock_toast:
                    self.handler._send_windows_notification("Title", "Message", False, 10)
                    mock_toast.assert_called_once()

    def test_fullscreen_overlay(self):
        """Test fullscreen overlay for severe escalations."""
        content = {
            'task_title': 'Critical Task',
            'time_off_task': '15 minutes',
            'impact': 'High productivity loss',
            'suggestions': ['Close browser', 'Open work app']
        }

        with patch.object(self.handler, 'send_notification') as mock_send:
            self.handler.show_fullscreen_overlay(content)

            # Should send high-priority notification
            mock_send.assert_called_once()
            call_args = mock_send.call_args[1]
            self.assertEqual(call_args['urgency'], 'high')
            self.assertTrue(call_args['sound'])
            self.assertTrue(call_args['require_acknowledgment'])


class TestIntegration(unittest.TestCase):
    """Integration tests for the escalation system."""

    def test_full_escalation_cycle(self):
        """Test a complete escalation and de-escalation cycle."""
        manager = EscalationManager()
        task_id = "integration-test-task"

        # Phase 1: Escalate to SEVERE
        history = []
        escalation_levels = []

        for i in range(12):
            result = AIAnalysisResult(
                id=f"analysis-{i}",
                screenshot_id=f"screenshot-{i}",
                task_id=task_id,
                is_aligned=False,
                confidence=0.9,
                reasoning="User distracted",
                suggestions=["Return to task"]
            )
            history.insert(0, result)
            level = manager.check_escalation_needed(history)
            escalation_levels.append(level)

        # Should have escalated through all levels
        self.assertIn(EscalationLevel.MILD, escalation_levels)
        self.assertIn(EscalationLevel.MODERATE, escalation_levels)
        self.assertIn(EscalationLevel.SEVERE, escalation_levels)
        self.assertEqual(manager.get_current_level(), EscalationLevel.SEVERE)

        # Phase 2: De-escalate
        for i in range(5):
            result = AIAnalysisResult(
                id=f"analysis-aligned-{i}",
                screenshot_id=f"screenshot-aligned-{i}",
                task_id=task_id,
                is_aligned=True,
                confidence=0.95,
                reasoning="User on task",
                suggestions=[]
            )
            history.insert(0, result)
            level = manager.check_escalation_needed(history)

        # Should have de-escalated
        self.assertLess(manager.get_current_level().value, EscalationLevel.SEVERE.value)

        # Phase 3: Full reset after cooldown
        manager.last_escalation_time = datetime.now() - timedelta(minutes=10)

        for i in range(3):
            result = AIAnalysisResult(
                id=f"analysis-final-{i}",
                screenshot_id=f"screenshot-final-{i}",
                task_id=task_id,
                is_aligned=True,
                confidence=0.95,
                reasoning="User on task",
                suggestions=[]
            )
            history.insert(0, result)
            level = manager.check_escalation_needed(history)

        # Should be fully reset
        self.assertEqual(manager.get_current_level(), EscalationLevel.NONE)


if __name__ == '__main__':
    unittest.main()
