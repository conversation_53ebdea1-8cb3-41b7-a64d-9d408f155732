#!/usr/bin/env python3
"""
Productivity Guard - Main Pipeline Orchestrator

This is the main entry point that ties together all features:
- Task Management
- Screenshot Capture  
- AI Reasoning & Enforcement
- Escalation Mode
- Data & Privacy
"""

import argparse
import signal
import sys
import time
from datetime import datetime
from pathlib import Path

from src.agents.ai_reasoning import AIReasoning
from src.apis.data_privacy import DataStore, PrivacyManager
from src.agents.escalation import EscalationManager
from src.screenshot_taker import ScreenshotCapture, ScreenshotConfig

# Import all feature modules
from shared import Config, ensure_directory, get_logger
from shared import Task as SharedTask
from src.apis.task_management import Task, TaskManager, TaskStatus

logger = get_logger(__name__)


class ProductivityGuard:
    """Main orchestrator for the Productivity Guard system."""

    def __init__(self, config: Config | None = None):
        """Initialize the Productivity Guard system."""
        self.config = config or Config()
        self.running = False

        # Ensure data directory exists
        ensure_directory(self.config.data_directory)

        # Initialize all components
        logger.info("Initializing Productivity Guard components...")

        # Task Management
        self.task_manager = TaskManager(
            db_path=str(self.config.data_directory / "tasks.db")
        )

        # Screenshot Capture
        screenshot_config = ScreenshotConfig(
            capture_interval=self.config.screenshot_interval,
            output_directory=str(self.config.data_directory / "screenshots"),
            quality=self.config.screenshot_quality,
            monitor_index=self.config.monitors_to_capture[0] if self.config.monitors_to_capture else None
        )
        self.screenshot_capture = ScreenshotCapture(screenshot_config)

        # AI Reasoning
        from src.agents.ai_reasoning.ai_reasoning import AIConfig
        ai_config = AIConfig(provider="auto")  # Use auto provider selection
        self.ai_reasoning = AIReasoning(config=ai_config)

        # Escalation Management
        self.escalation_manager = EscalationManager(
            config={"severe_threshold": self.config.escalation_threshold}
        )

        # Data & Privacy
        self.data_store = DataStore(
            db_path=str(self.config.data_directory / "data.db"),
            password="demo_key" if self.config.enable_privacy_mode else None
        )
        self.privacy_manager = PrivacyManager()

        # Set up signal handlers
        signal.signal(signal.SIGINT, self._handle_shutdown)
        signal.signal(signal.SIGTERM, self._handle_shutdown)

        logger.info("Productivity Guard initialized successfully")

    def _convert_to_shared_task(self, task: Task) -> SharedTask:
        """Convert a task_management.Task to shared.Task for AI reasoning."""
        # Handle both task.id and task.task_id
        task_id = getattr(task, 'id', None) or getattr(task, 'task_id', None)
        if not task_id:
            task_id = f"task-{task.title[:8]}"

        # Handle status - could be enum or string
        status = task.status
        if hasattr(status, 'value'):
            status = status.value

        return SharedTask(
            id=str(task_id),
            title=task.title,
            priority=task.priority,
            description=task.description,
            status=status,
            created_at=task.created_at,
            started_at=task.started_at,
            completed_at=task.completed_at,
            tags=task.tags
        )

    def _handle_shutdown(self, signum, frame):
        """Handle graceful shutdown."""
        logger.info("Shutdown signal received. Stopping Productivity Guard...")
        self.stop()
        sys.exit(0)

    def start(self):
        """Start the Productivity Guard system."""
        logger.info("Starting Productivity Guard...")
        self.running = True

        # Load today's tasks
        current_task = self.task_manager.get_current_task()
        if not current_task:
            logger.warning("No active task found. Please set an active task.")
            # For demo, create a sample task
            sample_task = Task(
                id="demo-task-001",
                title="Complete important work",
                priority=5,
                description="Demo task for testing",
                status="pending"
            )
            self.task_manager.add_task(sample_task)
            task_index = len(self.task_manager.tasks) - 1
            self.task_manager.update_task_status(task_index, TaskStatus.ACTIVE)
            current_task = sample_task
            logger.info(f"Created demo task: {current_task.title}")

        logger.info(f"Current task: {current_task.title}")

        # Start the main monitoring loop
        self._run_monitoring_loop()

    def _run_monitoring_loop(self):
        """Main monitoring loop that orchestrates all features."""
        logger.info("Starting monitoring loop...")

        while self.running:
            try:
                # Step 1: Get current task
                current_task = self.task_manager.get_current_task()
                if not current_task:
                    logger.warning("No active task. Skipping this cycle.")
                    time.sleep(self.config.screenshot_interval)
                    continue

                # Step 2: Capture screenshot
                logger.debug("Capturing screenshot...")
                screenshot = self.screenshot_capture.capture()

                # Step 3: Save screenshot (with privacy redaction if enabled)
                if self.config.enable_privacy_mode:
                    # Detect sensitive regions
                    # In a real implementation, this would analyze the image
                    # For now, we'll save as-is
                    pass

                self.data_store.save_screenshot(screenshot.image_path)
                logger.debug(f"Screenshot saved: {screenshot.id}")

                # Step 4: Analyze screenshot against current task
                logger.debug("Analyzing screenshot...")
                # Convert task to shared format for AI reasoning
                shared_task = self._convert_to_shared_task(current_task)
                analysis_result = self.ai_reasoning.analyze_screenshot(
                    screenshot, shared_task
                )

                # Step 5: Save analysis result
                self.data_store.save_analysis(analysis_result, screenshot.id)
                logger.info(
                    f"Analysis complete - Aligned: {analysis_result.is_aligned}, "
                    f"Confidence: {analysis_result.confidence:.2f}"
                )

                # Step 6: Check if escalation is needed
                if not analysis_result.is_aligned:
                    # Get recent history for escalation decision
                    history = self.data_store.get_history(hours=1)
                    recent_results = [result for _, result in history[-10:]]

                    escalation_level = self.escalation_manager.check_escalation_needed(
                        recent_results
                    )

                    if escalation_level.value > 0:
                        logger.warning(f"Escalation triggered: {escalation_level.name}")
                        self.escalation_manager.execute_escalation(escalation_level)

                # Step 7: Enforce retention policies periodically
                if time.time() % 3600 < self.config.screenshot_interval:  # Once per hour
                    self.data_store.enforce_retention_policies()

                # Wait for next interval
                time.sleep(self.config.screenshot_interval)

            except KeyboardInterrupt:
                logger.info("Monitoring loop interrupted by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}", exc_info=True)
                time.sleep(self.config.screenshot_interval)

    def stop(self):
        """Stop the Productivity Guard system."""
        logger.info("Stopping Productivity Guard...")
        self.running = False

        # Stop screenshot capture if running
        if self.screenshot_capture.is_capturing():
            self.screenshot_capture.stop_capture_loop()

        # Save any pending data
        logger.info("Productivity Guard stopped")

    def get_statistics(self) -> dict:
        """Get system statistics."""
        stats = {
            "total_screenshots": len(self.data_store.get_history(hours=24)),
            "current_task": self.task_manager.get_current_task(),
            "escalation_stats": self.escalation_manager.get_escalation_stats(),
            "active_since": datetime.now()
        }
        return stats


def main():
    """Main entry point with CLI interface."""
    parser = argparse.ArgumentParser(
        description="Productivity Guard - AI-driven focus companion"
    )
    parser.add_argument(
        "--config",
        type=Path,
        help="Path to configuration file"
    )
    parser.add_argument(
        "--interval",
        type=int,
        default=5,
        help="Screenshot interval in seconds (default: 5)"
    )
    parser.add_argument(
        "--no-privacy",
        action="store_true",
        help="Disable privacy mode"
    )
    parser.add_argument(
        "--task-file",
        type=Path,
        help="Import tasks from file (CSV, JSON, or text)"
    )
    parser.add_argument(
        "--demo",
        action="store_true",
        help="Run in demo mode with sample data"
    )

    args = parser.parse_args()

    # Create configuration
    config = Config(
        screenshot_interval=args.interval,
        enable_privacy_mode=not args.no_privacy
    )

    # Initialize Productivity Guard
    guard = ProductivityGuard(config)

    # Import tasks if provided
    if args.task_file and args.task_file.exists():
        try:
            guard.task_manager.load_tasks(str(args.task_file))
            logger.info(f"Loaded tasks from {args.task_file}")
        except Exception as e:
            logger.error(f"Failed to load tasks: {e}")

    # Run demo mode if requested
    if args.demo:
        logger.info("Running in demo mode...")
        demo_runner(guard)
    else:
        # Start the system
        guard.start()


def demo_runner(guard: ProductivityGuard):
    """Run a demo showing all features working together."""
    print("\n" + "="*60)
    print("PRODUCTIVITY GUARD DEMO")
    print("="*60 + "\n")

    # Show current configuration
    print("Configuration:")
    print(f"  - Screenshot interval: {guard.config.screenshot_interval}s")
    print(f"  - Privacy mode: {'Enabled' if guard.config.enable_privacy_mode else 'Disabled'}")
    print(f"  - Data directory: {guard.config.data_directory}")
    print()

    # Create demo tasks
    print("Creating demo tasks...")
    tasks = [
        Task(
            id="task-001",
            title="Write project documentation",
            priority=5,
            description="Complete the PRD and technical docs",
            status=TaskStatus.ACTIVE.value
        ),
        Task(
            id="task-002",
            title="Review pull requests",
            priority=4,
            description="Review team PRs on GitHub",
            status=TaskStatus.PENDING.value
        ),
        Task(
            id="task-003",
            title="Update dependencies",
            priority=3,
            description="Update project dependencies",
            status=TaskStatus.PENDING.value
        )
    ]

    for task in tasks:
        guard.task_manager.add_task(task)
    print(f"  - Added {len(tasks)} tasks")
    print()

    # Run a few monitoring cycles
    print("Running monitoring cycles...")
    for i in range(3):
        print(f"\nCycle {i+1}:")

        # Get current task
        current_task = guard.task_manager.get_current_task()
        print(f"  - Current task: {current_task.title}")

        # Capture screenshot
        screenshot = guard.screenshot_capture.capture()
        print(f"  - Screenshot captured: {screenshot.id}")

        # Analyze
        shared_task = guard._convert_to_shared_task(current_task)
        result = guard.ai_reasoning.analyze_screenshot(screenshot, shared_task)
        print(f"  - Analysis: {'Aligned' if result.is_aligned else 'Not aligned'} "
              f"(confidence: {result.confidence:.2f})")
        print(f"  - Reasoning: {result.reasoning}")

        if result.suggestions:
            print(f"  - Suggestions: {', '.join(result.suggestions)}")

        # Save data
        guard.data_store.save_screenshot(screenshot.image_path)
        guard.data_store.save_analysis(result, screenshot.id)

        time.sleep(1)

    # Show statistics
    print("\n" + "-"*60)
    print("Statistics:")
    stats = guard.get_statistics()
    print(f"  - Total screenshots: {stats['total_screenshots']}")
    print(f"  - Escalation stats: {stats['escalation_stats']}")

    print("\n" + "="*60)
    print("Demo completed!")
    print("="*60 + "\n")


if __name__ == "__main__":
    # Example input: python main.py --interval 10 --demo
    # Example output: Runs demo showing all features working together
    main()
