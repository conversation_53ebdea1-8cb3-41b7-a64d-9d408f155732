#!/usr/bin/env python3
"""Test script to verify real LLM integration works."""

import os
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from src.agents.ai_reasoning import AIReasoning, AIConfig
from src.screenshot_taker import ScreenshotCapture, ScreenshotConfig
from shared.types import Task


def test_real_llm_analysis():
    """Test real LLM analysis with a screenshot."""
    print("🧪 Testing Real LLM Integration")
    print("=" * 50)
    
    # Check if API keys are available
    has_openai = bool(os.getenv("OPENAI_API_KEY"))
    has_anthropic = bool(os.getenv("ANTHROPIC_API_KEY"))
    
    print(f"OpenAI API Key: {'✅ Available' if has_openai else '❌ Missing'}")
    print(f"Anthropic API Key: {'✅ Available' if has_anthropic else '❌ Missing'}")
    
    if not (has_openai or has_anthropic):
        print("\n❌ No API keys found. Testing will use mock mode.")
        provider = "mock"
    else:
        provider = "anthropic" if has_anthropic else "openai"
        print(f"\n✅ Using {provider} provider")
    
    # Create AI config
    config = AIConfig(
        provider=provider,
        enable_fallback_mock=True,
        confidence_threshold_high=0.8,
        confidence_threshold_medium=0.5
    )
    
    # Initialize AI reasoning
    try:
        ai_reasoning = AIReasoning(config)
        print(f"✅ AI Reasoning initialized")
        
        # Get model info
        model_info = ai_reasoning.get_model_info()
        print(f"Model: {model_info.get('model_name', 'Unknown')}")
        print(f"Type: {model_info.get('model_type', 'Unknown')}")
        print(f"Provider: {model_info.get('provider', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ Failed to initialize AI reasoning: {e}")
        return False
    
    # Capture a screenshot
    print("\n📸 Capturing screenshot...")
    try:
        screenshot_config = ScreenshotConfig(
            output_directory="./output/test_screenshots",
            filename_prefix="test_llm",
            image_format="jpg",
            quality=85
        )
        
        screenshot_capture = ScreenshotCapture(screenshot_config)
        screenshot = screenshot_capture.capture()
        
        print(f"✅ Screenshot captured: {screenshot.id}")
        print(f"   Path: {screenshot.image_path}")
        print(f"   Size: {screenshot.file_size} bytes")
        
    except Exception as e:
        print(f"❌ Failed to capture screenshot: {e}")
        return False
    
    # Create a test task
    task = Task(
        id="test-task-001",
        title="Test LLM integration",
        description="Testing the real LLM integration for screenshot analysis",
        priority=5,
        status="active",
        tags=["testing", "development"]
    )
    
    print(f"\n🎯 Task: {task.title}")
    print(f"   Description: {task.description}")
    print(f"   Priority: {task.priority}/5")
    
    # Analyze screenshot
    print("\n🤖 Analyzing screenshot with LLM...")
    try:
        start_time = datetime.now()
        result = ai_reasoning.analyze_screenshot(screenshot, task)
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds() * 1000
        
        print(f"✅ Analysis complete!")
        print(f"   Aligned: {'✅ Yes' if result.is_aligned else '❌ No'}")
        print(f"   Confidence: {result.confidence:.3f}")
        print(f"   Processing time: {processing_time:.0f}ms")
        print(f"   Reasoning: {result.reasoning[:200]}...")
        
        if result.suggestions:
            print(f"   Suggestions:")
            for i, suggestion in enumerate(result.suggestions, 1):
                print(f"     {i}. {suggestion}")
        
        # Check if it's real or mock
        is_mock = result.reasoning.startswith("[MOCK]")
        print(f"   Mode: {'🎭 Mock' if is_mock else '🧠 Real LLM'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_provider_availability():
    """Test which providers are available."""
    print("\n🔍 Testing Provider Availability")
    print("=" * 50)
    
    from src.agents.ai_reasoning.providers.factory import get_available_providers
    
    available = get_available_providers()
    print(f"Available providers: {available}")
    
    if not available:
        print("❌ No providers available")
        return False
    
    for provider in available:
        try:
            from src.agents.ai_reasoning.providers.factory import create_provider
            p = create_provider(provider)
            info = p.get_model_info()
            print(f"✅ {provider}: {info['model']} ({info['provider']})")
        except Exception as e:
            print(f"❌ {provider}: {e}")
    
    return True


if __name__ == "__main__":
    print("🚀 Real LLM Integration Test")
    print("=" * 60)
    
    # Test provider availability
    provider_ok = test_provider_availability()
    
    # Test real analysis
    analysis_ok = test_real_llm_analysis()
    
    print("\n" + "=" * 60)
    if provider_ok and analysis_ok:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed")
    
    print("=" * 60)
