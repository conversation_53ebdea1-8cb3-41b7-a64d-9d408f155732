#!/usr/bin/env python3
"""Shared types and utilities for Productivity Guard system."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any
import sys
import logging

# Try to use loguru for better logging, fall back to standard logging
try:
    from loguru import logger
    # Configure colorized logging with loguru
    logger.remove()
    logger.add(sys.stderr, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan> | {message}", level="INFO", colorize=True)
    USE_LOGURU = True
except ImportError:
    # Fall back to standard logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    USE_LOGURU = False


@dataclass
class Config:
    """Global configuration for Productivity Guard."""
    screenshot_interval: int = 5  # seconds
    ai_model: str = "gpt-4"
    escalation_threshold: int = 3  # consecutive misalignments
    data_retention_days: int = 30
    screenshot_quality: int = 80  # JPEG quality 1-100
    monitors_to_capture: list[int] = field(default_factory=lambda: [0])  # Monitor indices
    data_directory: Path = field(default_factory=lambda: Path.home() / ".productivity_guard")
    enable_privacy_mode: bool = True
    notification_sound: bool = True
    max_escalation_level: str = "SEVERE"


@dataclass
class Task:
    """Represents a user task."""
    id: str
    title: str
    priority: int  # 1-5, higher is more important
    description: str
    status: str  # "active", "completed", "paused", "abandoned"
    created_at: datetime = field(default_factory=datetime.now)
    started_at: datetime | None = None
    completed_at: datetime | None = None
    tags: list[str] = field(default_factory=list)


@dataclass
class Screenshot:
    """Represents a captured screenshot."""
    id: str
    timestamp: datetime
    image_path: str
    monitor_id: int
    file_size: int  # bytes
    is_redacted: bool = False
    metadata: dict[str, Any] = field(default_factory=dict)


class EscalationLevel(Enum):
    """Escalation levels for user interventions."""
    NONE = 0
    MILD = 1
    MODERATE = 2
    SEVERE = 3


@dataclass
class AIAnalysisResult:
    """Result of AI analysis on screenshot."""
    id: str
    screenshot_id: str
    task_id: str
    is_aligned: bool
    confidence: float  # 0.0 to 1.0
    reasoning: str
    suggestions: list[str]
    analyzed_at: datetime = field(default_factory=datetime.now)
    processing_time_ms: int = 0


@dataclass
class NotificationEvent:
    """Represents a notification event."""
    id: str
    level: EscalationLevel
    message: str
    timestamp: datetime
    was_acknowledged: bool = False
    acknowledged_at: datetime | None = None


def get_logger(name: str):
    """Get a logger instance with the given name."""
    if USE_LOGURU:
        return logger.bind(name=name)
    else:
        return logging.getLogger(name)


def ensure_directory(path: Path) -> None:
    """Ensure a directory exists, creating it if necessary."""
    path.mkdir(parents=True, exist_ok=True)


if __name__ == "__main__":
    # Example usage demonstrating the types

    # Create example task
    task = Task(
        id="task-001",
        title="Complete PRD review",
        priority=5,
        description="Review and finalize the product requirements document",
        status="active"
    )
    print(f"Example Task: {task.title} (Priority: {task.priority})")

    # Create example screenshot
    screenshot = Screenshot(
        id="screenshot-001",
        timestamp=datetime.now(),
        image_path="/tmp/screenshot.jpg",
        monitor_id=0,
        file_size=1024000
    )
    print(f"Example Screenshot: {screenshot.id} at {screenshot.timestamp}")

    # Create example analysis result
    result = AIAnalysisResult(
        id="analysis-001",
        screenshot_id=screenshot.id,
        task_id=task.id,
        is_aligned=False,
        confidence=0.85,
        reasoning="User appears to be browsing social media instead of working on PRD",
        suggestions=["Close social media tabs", "Focus on document editing application"]
    )
    print(f"Example Analysis: Aligned={result.is_aligned}, Confidence={result.confidence}")

    # Demonstrate config
    config = Config()
    print(f"Example Config: Screenshot interval={config.screenshot_interval}s")
