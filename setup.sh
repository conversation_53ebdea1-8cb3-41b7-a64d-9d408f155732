#!/bin/bash
set -e

echo "Setting up Productivity Guard development environment..."

# Update system packages
sudo apt-get update -qq

# Install Python 3.12 and pip if not available
sudo apt-get install -y python3.12 python3.12-venv python3.12-dev python3-pip

# Install system dependencies for screenshot capture and GUI libraries
sudo apt-get install -y \
    libx11-dev \
    libxrandr-dev \
    libxinerama-dev \
    libxcursor-dev \
    libxi-dev \
    libxext-dev \
    libxfixes-dev \
    python3-tk \
    xvfb

# Create virtual environment
python3.12 -m venv .venv
source .venv/bin/activate

# Upgrade pip and install build tools
pip install --upgrade pip setuptools wheel

# Install the project in development mode with all dependencies
pip install -e ".[dev]"

# Verify installation
python -c "import sys; print(f'Python version: {sys.version}')"
python -c "import pytest; print(f'pytest version: {pytest.__version__}')"

# Add virtual environment activation to profile
echo "source $(pwd)/.venv/bin/activate" >> $HOME/.profile

echo "Setup completed successfully!"