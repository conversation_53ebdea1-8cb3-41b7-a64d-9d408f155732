#!/usr/bin/env python3
"""Test the justification system implementation."""

import time
import sys
from src.agents.justification import Just<PERSON><PERSON>ana<PERSON>, JustificationConfig, JustificationStatus

def test_justification_system():
    """Test the justification prompt system."""
    print("="*60)
    print("TESTING JUSTIFICATION SYSTEM")
    print("="*60)
    print()
    
    # Configure for terminal prompt with shorter timeout
    config = JustificationConfig(
        timeout_seconds=10,
        use_gui=False,  # Use terminal for testing
        prompt_message="It looks like you've switched tasks. Why?"
    )
    
    manager = JustificationManager(config)
    
    print("Scenario: You're supposed to be working on 'Code Review'")
    print("but the system detected you're on social media.")
    print()
    print("A justification prompt will appear in 2 seconds...")
    print("You have 10 seconds to respond.")
    print()
    
    time.sleep(2)
    
    # Prompt user
    result = manager.prompt_for_justification(
        task_name="Code Review",
        callback=lambda r: print(f"\n[Callback] Received result: {r.status.value}")
    )
    
    print()
    print("="*60)
    print("RESULTS:")
    print("="*60)
    print(f"Status: {result.status.value}")
    print(f"Response time: {result.response_time_seconds:.1f} seconds")
    
    if result.justification_text:
        print(f"Justification: '{result.justification_text}'")
        is_valid = manager.is_valid_justification(result.justification_text)
        print(f"Valid justification: {is_valid}")
        
        if is_valid:
            print("\n✓ Valid justification accepted - no escalation needed")
        else:
            print("\n✗ Invalid justification - escalation would be triggered")
    else:
        print("\n✗ No justification provided - escalation would be triggered")
    
    print()
    print("This demonstrates requirements F-7, F-8, and F-9:")
    print("- F-7: Prompt user when misalignment detected")
    print("- F-8: Accept justification text")
    print("- F-9: 60-second timeout (10s for this test)")

if __name__ == "__main__":
    test_justification_system()