[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "productivity-guard"
version = "0.1.0"
description = "AI-driven productivity companion that helps users stay focused on their highest-priority tasks"
readme = "README_PRD.md"
requires-python = ">=3.12"
authors = [
    {name = "Productivity Guard Team", email = "<EMAIL>"}
]
keywords = ["productivity", "ai", "focus", "task-management", "screenshot-monitoring"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "Topic :: Office/Business :: Task Management",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Core dependencies
    "mss>=9.0.1", # Screenshot capture
    "Pillow>=10.0.0", # Image processing
    "plyer>=2.1", # Cross-platform notifications
    "cryptography>=41.0.0", # Encryption for privacy
    # LLM dependencies
    "openai>=1.0.0", # OpenAI API client
    "anthropic>=0.8.0", # Anthropic API client
    "requests>=2.31.0", # HTTP requests
    # Development dependencies
    "mypy>=1.7.0", # Type checking
    "pytest>=7.4.0", # Testing framework
    "pytest-cov>=4.1.0", # Test coverage
    "black>=23.0.0", # Code formatting
    "ruff>=0.1.0", # Linting
    "colorlog>=6.9.0",
    "loguru>=0.7.3",
]

[project.optional-dependencies]
dev = [
    "pytest-mock>=3.11.1",
    "pytest-asyncio>=0.21.1",
    "coverage[toml]>=7.3.0",
]

[project.scripts]
productivity-guard = "main:main"

[tool.setuptools]
packages = [
    "shared",
    "src.screenshot_taker",
    "src.apis.task_management",
    "src.apis.data_privacy", 
    "src.agents.ai_reasoning",
    "src.agents.escalation"
]

[tool.mypy]
python_version = "3.12"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --cov=. --cov-report=html --cov-report=term-missing"

[tool.black]
line-length = 100
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
line-length = 100
target-version = "py312"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = []
fixable = ["ALL"]
unfixable = []
exclude = [
    ".git",
    ".mypy_cache",
    ".ruff_cache",
    ".venv",
    "__pypackages__",
    "build",
    "dist",
]
