plyer-2.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
plyer-2.1.0.dist-info/LICENSE,sha256=KCs9hzXvbL3PjMSAlH1Q4K6krRzJMdQfP2prwjlW8tY,1081
plyer-2.1.0.dist-info/METADATA,sha256=AtjxKQlj6oe1ojERaGoh6QNmZcRAlRMhqrkdDPLGJRQ,61160
plyer-2.1.0.dist-info/RECORD,,
plyer-2.1.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer-2.1.0.dist-info/WHEEL,sha256=bb2Ot9scclHKMOLDEHY6B2sicWOgugjFKaJsT7vwMQo,110
plyer-2.1.0.dist-info/top_level.txt,sha256=Lr8S6kq-BqOuOALT9toNPJH65Xl966rafe3MZuyh4Yo,6
plyer/__init__.py,sha256=72RidHRz-fqZpfsD2ttSVdmc0K4Xuw9_Iv31eNvBzps,4069
plyer/__pycache__/__init__.cpython-313.pyc,,
plyer/__pycache__/utils.cpython-313.pyc,,
plyer/facades/__init__.py,sha256=I-OBQpEIKr8ocEhv35AQh9fP1C6iLuSXyEp-6-9IU9I,2088
plyer/facades/__pycache__/__init__.cpython-313.pyc,,
plyer/facades/__pycache__/accelerometer.cpython-313.pyc,,
plyer/facades/__pycache__/audio.cpython-313.pyc,,
plyer/facades/__pycache__/barometer.cpython-313.pyc,,
plyer/facades/__pycache__/battery.cpython-313.pyc,,
plyer/facades/__pycache__/bluetooth.cpython-313.pyc,,
plyer/facades/__pycache__/brightness.cpython-313.pyc,,
plyer/facades/__pycache__/call.cpython-313.pyc,,
plyer/facades/__pycache__/camera.cpython-313.pyc,,
plyer/facades/__pycache__/compass.cpython-313.pyc,,
plyer/facades/__pycache__/cpu.cpython-313.pyc,,
plyer/facades/__pycache__/devicename.cpython-313.pyc,,
plyer/facades/__pycache__/email.cpython-313.pyc,,
plyer/facades/__pycache__/filechooser.cpython-313.pyc,,
plyer/facades/__pycache__/flash.cpython-313.pyc,,
plyer/facades/__pycache__/gps.cpython-313.pyc,,
plyer/facades/__pycache__/gravity.cpython-313.pyc,,
plyer/facades/__pycache__/gyroscope.cpython-313.pyc,,
plyer/facades/__pycache__/humidity.cpython-313.pyc,,
plyer/facades/__pycache__/irblaster.cpython-313.pyc,,
plyer/facades/__pycache__/keystore.cpython-313.pyc,,
plyer/facades/__pycache__/light.cpython-313.pyc,,
plyer/facades/__pycache__/notification.cpython-313.pyc,,
plyer/facades/__pycache__/orientation.cpython-313.pyc,,
plyer/facades/__pycache__/processors.cpython-313.pyc,,
plyer/facades/__pycache__/proximity.cpython-313.pyc,,
plyer/facades/__pycache__/screenshot.cpython-313.pyc,,
plyer/facades/__pycache__/sms.cpython-313.pyc,,
plyer/facades/__pycache__/spatialorientation.cpython-313.pyc,,
plyer/facades/__pycache__/storagepath.cpython-313.pyc,,
plyer/facades/__pycache__/stt.cpython-313.pyc,,
plyer/facades/__pycache__/temperature.cpython-313.pyc,,
plyer/facades/__pycache__/tts.cpython-313.pyc,,
plyer/facades/__pycache__/uniqueid.cpython-313.pyc,,
plyer/facades/__pycache__/vibrator.cpython-313.pyc,,
plyer/facades/__pycache__/wifi.cpython-313.pyc,,
plyer/facades/accelerometer.py,sha256=Tz-PIPLyuI1rRFLTQcf65FEGleuecw5JjxIakRO2BLw,1626
plyer/facades/audio.py,sha256=KxTDO5numBEdoMBwcmSrz6d6Nf9wJJUFG9L8hT2eGL4,1911
plyer/facades/barometer.py,sha256=MzJAe2Geb0wQ1gxNWslJUaP3tQ3aP137AZXp_RC8yjQ,867
plyer/facades/battery.py,sha256=KG5EmVvxZe80sA4yHSb22Hvv9Ss1L3EgeYQ2bP6ah1A,1132
plyer/facades/bluetooth.py,sha256=zc8gKJ4krKopZXvC0vjyNrnqLiGlFTn_GaT90mB9zj8,745
plyer/facades/brightness.py,sha256=tCoNVQXzH6IWgX-qKNVkIb1CJHpJy7JWauLWMrpddmg,1269
plyer/facades/call.py,sha256=SKYWm39YobSAei_CIxDW9kbDphfH5swBjVGoFHFhwg0,1003
plyer/facades/camera.py,sha256=0eOhSkSkKhUVHZXbGOualaYQQjZoSaNRqn3TQZoOWfM,2376
plyer/facades/compass.py,sha256=reM0uBELvmiBvDrXtIpGhSf46Alg_Xc9lL1N8CI6Zcs,2540
plyer/facades/cpu.py,sha256=N62r8PkUE4--Cqve60VZeM-TO6dne9Hihj-vq78NVd4,1975
plyer/facades/devicename.py,sha256=42tVZM8d3KOz4YzprAiPAfRugMuX7EDZCiksl0ZnEW8,794
plyer/facades/email.py,sha256=oDUfMw35GbvPkkrwTFPJSN0mIoR_5MpElunuoDapMIY,1460
plyer/facades/filechooser.py,sha256=JIah32q3TocdAx3V2bbM6hPvbHrlZVN0yzRIcGJANyE,2723
plyer/facades/flash.py,sha256=YIwmz__anU8s5TXij91-Xq8ZHRYRVn1ph4BtgLqbBOA,1231
plyer/facades/gps.py,sha256=bH3Yeduq1SZBMHGQjdoq1OAFJALMj1uWfkmDEnAAq1w,2635
plyer/facades/gravity.py,sha256=_GyJIMWCTDeTwdIuSrBhEggX42DL7kU8U46iKjek2No,843
plyer/facades/gyroscope.py,sha256=DpZDqcQBcLvMuP4KBKDyv4_pKVBwBIkGU1BZQlsJkXM,3203
plyer/facades/humidity.py,sha256=U9fEC4swYS_4IXtpmnoiWup7XUnWLqE_B315G3hrsmw,828
plyer/facades/irblaster.py,sha256=mN5nspocJcJHqXx-BylC_uGmmOSGWmR_hZxaI8gpLdg,2551
plyer/facades/keystore.py,sha256=3riWyag828U8jRcDHrjmPtN2kkBJjLDiUS4GKTClxL8,869
plyer/facades/light.py,sha256=qpjaS0YXw1x0rKgDcrUDjzKiB_TtHIoMH0UPO4obaGs,923
plyer/facades/notification.py,sha256=aoCeW06YlsIUadhc-BLmfpUMLz43S82RkYxxo6_z6S4,2697
plyer/facades/orientation.py,sha256=NziivnQfH6AnoLY6zLmJMoHH9epTnU8_2rJDowl_LV0,1878
plyer/facades/processors.py,sha256=7BfnJTvXV7qyJegKtSqU0szJn70-HrNyVX_u5bjwuq8,934
plyer/facades/proximity.py,sha256=99M76GJG6Svja2Pj_UbFNQTaoHOXYcobgqWId4acTfE,1200
plyer/facades/screenshot.py,sha256=qACQV1G-Xz6EdBX6CJV-mxSwULm_AkbMLOVpqjmPHNo,1030
plyer/facades/sms.py,sha256=xKopE4RKM8qeMnCWsnNPxUmy4u2N2yZHVOWti9yBOfk,896
plyer/facades/spatialorientation.py,sha256=POrT-x8psr1NkiI52X0SMm_VJCCB8rblyRxRv_GcDwc,1652
plyer/facades/storagepath.py,sha256=VxAPTYnZ5Rfo7ps5RlZHV5oFw7ooNUshmyQhY97x5Tk,3398
plyer/facades/stt.py,sha256=JMASgZ7k_TqX4PbhtDdHktl_71zare6RFfD4b2KbvZQ,4569
plyer/facades/temperature.py,sha256=Swbfw7N1wqICZQkn6i2ZDgQ_4yI2D43NUVC3N5ZPXHc,959
plyer/facades/tts.py,sha256=78kEHW7N32rm_6UmBwngmXQHJDWo3keJrv-asKoC4UE,656
plyer/facades/uniqueid.py,sha256=dEGAFm44emFoT1Ql2DbifLMjdyDX7AQwGxdipHpnrCc,1006
plyer/facades/vibrator.py,sha256=1CWe48hf_Q9txNPviHUe9fOQ2biyc5amD78LLRMrNJY,2197
plyer/facades/wifi.py,sha256=WKQYVKjhN9hmOaHD-KgdQZUm03IwUbyd3ZODhb1pDJE,4169
plyer/platforms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer/platforms/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/android/__init__.py,sha256=ZRkVLz0xwXJjt8yodjhCDmwntOGv4AAxWVCmiV2A07E,511
plyer/platforms/android/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/android/__pycache__/accelerometer.cpython-313.pyc,,
plyer/platforms/android/__pycache__/audio.cpython-313.pyc,,
plyer/platforms/android/__pycache__/barometer.cpython-313.pyc,,
plyer/platforms/android/__pycache__/battery.cpython-313.pyc,,
plyer/platforms/android/__pycache__/bluetooth.cpython-313.pyc,,
plyer/platforms/android/__pycache__/brightness.cpython-313.pyc,,
plyer/platforms/android/__pycache__/call.cpython-313.pyc,,
plyer/platforms/android/__pycache__/camera.cpython-313.pyc,,
plyer/platforms/android/__pycache__/compass.cpython-313.pyc,,
plyer/platforms/android/__pycache__/devicename.cpython-313.pyc,,
plyer/platforms/android/__pycache__/email.cpython-313.pyc,,
plyer/platforms/android/__pycache__/filechooser.cpython-313.pyc,,
plyer/platforms/android/__pycache__/flash.cpython-313.pyc,,
plyer/platforms/android/__pycache__/gps.cpython-313.pyc,,
plyer/platforms/android/__pycache__/gravity.cpython-313.pyc,,
plyer/platforms/android/__pycache__/gyroscope.cpython-313.pyc,,
plyer/platforms/android/__pycache__/humidity.cpython-313.pyc,,
plyer/platforms/android/__pycache__/irblaster.cpython-313.pyc,,
plyer/platforms/android/__pycache__/keystore.cpython-313.pyc,,
plyer/platforms/android/__pycache__/light.cpython-313.pyc,,
plyer/platforms/android/__pycache__/notification.cpython-313.pyc,,
plyer/platforms/android/__pycache__/orientation.cpython-313.pyc,,
plyer/platforms/android/__pycache__/proximity.cpython-313.pyc,,
plyer/platforms/android/__pycache__/sms.cpython-313.pyc,,
plyer/platforms/android/__pycache__/spatialorientation.cpython-313.pyc,,
plyer/platforms/android/__pycache__/storagepath.cpython-313.pyc,,
plyer/platforms/android/__pycache__/stt.cpython-313.pyc,,
plyer/platforms/android/__pycache__/temperature.cpython-313.pyc,,
plyer/platforms/android/__pycache__/tts.cpython-313.pyc,,
plyer/platforms/android/__pycache__/uniqueid.cpython-313.pyc,,
plyer/platforms/android/__pycache__/vibrator.cpython-313.pyc,,
plyer/platforms/android/accelerometer.py,sha256=e5ie5AQfqNNj3ae8xDvtRBMUabnfIDClKrECSIzxHv4,2126
plyer/platforms/android/audio.py,sha256=cSh_hvAcCUsHKIaa3ZskFThIxmHiSuXuRwCBZEaEteA,1658
plyer/platforms/android/barometer.py,sha256=OkNtVgOaMkbBD-i0SyqQWlUL8aj2PnXLaVG-8SNBgtg,1845
plyer/platforms/android/battery.py,sha256=QmD7WvBvUQnqFPepbWgrceLBKqIK-PvTMGiVoShbmFE,1318
plyer/platforms/android/bluetooth.py,sha256=G-ks2OjCzdYXXiuv7c3YIWNzwkCzHHloRo3K-q8FzVU,668
plyer/platforms/android/brightness.py,sha256=g-Yu5zHR8xIIvpFi79amn4JodECitO0jzgNrFKCL9qg,806
plyer/platforms/android/call.py,sha256=J0-8LhUjwakycAv7wDjtkJStbQrN_N3CwIIM8jBbPik,618
plyer/platforms/android/camera.py,sha256=lXFFmzv70jkrVkbWtgwrv1kVtak7CaSFgdpWvHjki6E,2164
plyer/platforms/android/compass.py,sha256=AxW8Q9rFCfIWWTPmhZHTqrJuHgFuS4b4ZRfEaGJgDBQ,3408
plyer/platforms/android/devicename.py,sha256=s4T5UK3AJRa_SugkEAH0RKnQRtT3Ak47dpmyw3WhwtE,807
plyer/platforms/android/email.py,sha256=rzKm7xVjIxZwCZQNlj7unC0sOKPgIRgVlFg57anDTFs,1556
plyer/platforms/android/filechooser.py,sha256=Q01a1_4pGU5IMfIu-2J6XywwepxsrMSjaI9QJ7PdyaM,15298
plyer/platforms/android/flash.py,sha256=duRLxqLMWB4FDSKZoVbmddQ0aiyIYyYnrRsp3_-aaeA,1511
plyer/platforms/android/gps.py,sha256=YYeVjUNn9QAbX-Zm4lweiPSEmBuRlxRd_YpBTtAKqf0,2689
plyer/platforms/android/gravity.py,sha256=fPGMhZshIX-I8JyUIXpyP4AnkKvspZjazEmOhe22dlU,2078
plyer/platforms/android/gyroscope.py,sha256=icoJi6Evea13MY0fSXQ-Ncz8eCS21ae-_M58YvsqLNA,3425
plyer/platforms/android/humidity.py,sha256=T9mKcDS0pBOdaGSL8VLny5sHBBQp-nDvnjdCODi6az4,3361
plyer/platforms/android/irblaster.py,sha256=cPPl8BBqaVkXCvzTqun8ymeuoXi5cIIlMpBkAKBBjds,1662
plyer/platforms/android/keystore.py,sha256=1S8IAZvm59foAkXdC3z4EjDRjsu9KKb5C-5gh1x6W_Q,771
plyer/platforms/android/light.py,sha256=H3oiN1eLeGsleUg9EvOXE_7uEnAogUcasYpaP-bEMEI,1755
plyer/platforms/android/notification.py,sha256=4LRGOJMsJa28xCmUJLG9qSEbSbdPnnxosJvrVKAGhpY,6285
plyer/platforms/android/orientation.py,sha256=FdWxDZbZ_oUX0qOsloO_UYoshxTrMNbuQjh1mdtx0PM,1417
plyer/platforms/android/proximity.py,sha256=Y7ZY5wVIeC0AJXq_UoONvehST3zzcQVjTkHemACvCpM,2029
plyer/platforms/android/sms.py,sha256=iiMlC38RQacqVAZRkhcjGbBWaFWvUynXioiSdX7d56U,456
plyer/platforms/android/spatialorientation.py,sha256=uT6pH1_HR_W8Dg3j6K4Wn3kRVG3CommO41MPHBBcR8s,3693
plyer/platforms/android/storagepath.py,sha256=vcKryNK9k9SCFQRGgJXLEaLfDY_KCsNE485d4ceAC2c,2087
plyer/platforms/android/stt.py,sha256=ypRIO8J3Ik56CUO2bSV2n0eJadZhppxWFsnrRbWLlrw,7456
plyer/platforms/android/temperature.py,sha256=MI2Kwhnn5ZIkT1frGPQmFsBe36arjo8sjgtDjRNONjU,1890
plyer/platforms/android/tts.py,sha256=GsRF1zQT_msmppxW5zB3Xk0R7fxQLsQ2OT0_I2mRrZE,938
plyer/platforms/android/uniqueid.py,sha256=wCh_86fXEHN9kt0Na1vD9nJc0sW2JiugGUIDu-dODnA,545
plyer/platforms/android/vibrator.py,sha256=B0EBhlANzFoFnOc-x-8CjgdeESZyz22XUjztcNftv4Y,1844
plyer/platforms/ios/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer/platforms/ios/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/accelerometer.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/barometer.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/battery.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/brightness.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/call.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/camera.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/compass.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/email.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/filechooser.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/flash.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/gps.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/gravity.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/gyroscope.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/keystore.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/sms.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/spatialorientation.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/storagepath.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/tts.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/uniqueid.cpython-313.pyc,,
plyer/platforms/ios/__pycache__/vibrator.cpython-313.pyc,,
plyer/platforms/ios/accelerometer.py,sha256=irOBlQZdyfZu3FR-lCcSIyTR-tVy9xYIdoslgjoDgOc,765
plyer/platforms/ios/barometer.py,sha256=P1hi6aqYLzwi6uWQ9QLvMqyw7S6B4sWXBkJ5NINSvqY,558
plyer/platforms/ios/battery.py,sha256=DpqCxE584J40Pl4p79Gx3Y4gDHv3TNI1jXiVDeofQ78,1082
plyer/platforms/ios/brightness.py,sha256=x6y4dUjvlPZabWGnG8VkT-zk8GLbylj-XZY8oRHDidk,552
plyer/platforms/ios/call.py,sha256=oW1pGchCdNZfcYb9Jl-5AJBm3y0d-4rYNKK-2V8ZPpA,590
plyer/platforms/ios/camera.py,sha256=wPUbEgFSp_bf0NxvrkWhg5VKQQP24DjtYB3f11-AfBU,1349
plyer/platforms/ios/compass.py,sha256=Te1TdCriaLtLXkktTEFsIFjgjyjEobCinfcXv3WZIHg,1071
plyer/platforms/ios/email.py,sha256=MiWJDhuRZM3AsOkYdSPjntsORPIe2G6MCnTWhNRFTCY,1209
plyer/platforms/ios/filechooser.py,sha256=8SzDd4yCr1xAayv6jQAz8UaKY7yV-A17fEBghMA5eKA,2705
plyer/platforms/ios/flash.py,sha256=rY3o1WuY9NfrSiYaplc3LG-plYN65M1lJtp4bX-0zso,1176
plyer/platforms/ios/gps.py,sha256=bNHFnf5rSWRvzIOAnUizwiY_kaWBy0AlJs7YBQgCW6M,2680
plyer/platforms/ios/gravity.py,sha256=WHW_SZBW0BruRPFlxpl1NOpQgCDu3vPGfIVOXwXGU8s,582
plyer/platforms/ios/gyroscope.py,sha256=vlUvTC8Jk3BTL3q_FYKvulEOnWanBHyOcEQ48IqhLTw,1469
plyer/platforms/ios/keystore.py,sha256=Mk3a-R5rmuzKbKeIum1B45KcsfuY6PSfAMrvAbq1VvY,635
plyer/platforms/ios/sms.py,sha256=tXIifs6Q7ZJghXP5f1nkyfvEdxoUA-_jRbxWUN0IkOs,1106
plyer/platforms/ios/spatialorientation.py,sha256=K-FVpNF0Y8WNqz2wBhfhJocQlOXUrQ02eGtrLVdQywI,684
plyer/platforms/ios/storagepath.py,sha256=5wqVJI-9zTHfsYreZWtc3mukoc-BnSkAuStTeU4EoDc,1891
plyer/platforms/ios/tts.py,sha256=zMeGkCd2_jb66lii2VBojQFumF7S9iSgrHpp9gaT7OU,1028
plyer/platforms/ios/uniqueid.py,sha256=m-KBcA2UwJy3xwNkWg6aqmC3zbDQI0fjozNTCnB9oEo,564
plyer/platforms/ios/vibrator.py,sha256=rzcWCBROMUuTfpeC3L_4bfOEs1yOBaInqEGzuc3gu60,940
plyer/platforms/linux/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer/platforms/linux/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/accelerometer.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/battery.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/brightness.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/cpu.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/devicename.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/email.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/filechooser.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/keystore.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/notification.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/orientation.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/processors.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/screenshot.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/storagepath.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/tts.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/uniqueid.cpython-313.pyc,,
plyer/platforms/linux/__pycache__/wifi.cpython-313.pyc,,
plyer/platforms/linux/accelerometer.py,sha256=XtK9xmyYhJoRMChzoGXB-s4v4ts9sUyzoQnB7xLCszE,870
plyer/platforms/linux/battery.py,sha256=WkmjrPihW7T4TAPw1q7tuDBkODxMABPZE4Z173r9deM,2949
plyer/platforms/linux/brightness.py,sha256=kCsRmQcP_7T0YAJ6aM0oW4Gh-DLj0NI6sFKXI7CSPHM,663
plyer/platforms/linux/cpu.py,sha256=G0ocQuJhrh2XAd1P7WO_T2-utbIUiiBA44tUArYZeq0,3477
plyer/platforms/linux/devicename.py,sha256=b1zfw0exCmVllB2pLcdRkQ-yISojdUUYUaHYI4UuLxU,390
plyer/platforms/linux/email.py,sha256=ZMG1g23HN_YahRc-zm-kmPBOLfdRsmVBAhcDB9AeEfc,1051
plyer/platforms/linux/filechooser.py,sha256=dfS_PP2_Z0bKDjDq31FlZ0diUAYS8OgWmuGYQFJ7tcg,7642
plyer/platforms/linux/keystore.py,sha256=kkYAt4Q6Wp0m5ku6QcTo7ak4W0fZJQQu5twI_oRB1ms,430
plyer/platforms/linux/notification.py,sha256=S5ehzPnEmUijiUUZwtL_dfGr6sDOZ2iLk0AJd7KXM5o,3443
plyer/platforms/linux/orientation.py,sha256=cbtOkFyQqwkZBGXxG2RETU8kqyFF_Xc2VyA_lsuzS54,870
plyer/platforms/linux/processors.py,sha256=q-c7iCYYYUQcCW6wAvhYpNXcjODdjw-u_rCZv-TXH_w,804
plyer/platforms/linux/screenshot.py,sha256=bJfKXisPavA0-a-cmb6vdSHYsSQlXyd-epNySYMIbC4,823
plyer/platforms/linux/storagepath.py,sha256=mos9WOW0e90EkwRJhEdKCAKpSi9C5Eukb5nf6OKkc_k,2047
plyer/platforms/linux/tts.py,sha256=o1HZd9dMpKFuyp2_xJl4cPh9BOPUbCYDEAyToiiAr_A,606
plyer/platforms/linux/uniqueid.py,sha256=naQW0wFRkBhMabldqoAR1uu6-9tdb_uQFD2vU4vyDx4,1016
plyer/platforms/linux/wifi.py,sha256=hly2i2jBWontSjftUOh7u559nEKwMQl9lzV4KVJ-w3Q,13472
plyer/platforms/macosx/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer/platforms/macosx/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/accelerometer.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/audio.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/battery.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/bluetooth.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/cpu.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/devicename.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/email.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/filechooser.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/keystore.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/notification.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/screenshot.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/storagepath.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/tts.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/uniqueid.cpython-313.pyc,,
plyer/platforms/macosx/__pycache__/wifi.cpython-313.pyc,,
plyer/platforms/macosx/accelerometer.py,sha256=1g7NpeHGn3-mHXnmvinapUd1MzIjzMFiVAMwqlJkEpM,539
plyer/platforms/macosx/audio.py,sha256=Jh2cgbOLileOhYTvPuGOavyBQFqwY0tBm3mVcU9tYnI,2710
plyer/platforms/macosx/battery.py,sha256=J-O9fEbuawiOHbh22CUcHCC0xflc9pTaUUw--hNk9-c,1542
plyer/platforms/macosx/bluetooth.py,sha256=bN-IP6ItOB9XHrWmZGoVLpaHFPtn4WIMKlkBypGkt-0,1211
plyer/platforms/macosx/cpu.py,sha256=SuYX4nBvhBCek59HGQA100p7zLglomO7U-7l29aBY08,1224
plyer/platforms/macosx/devicename.py,sha256=m0KuIfQqMaAyeAZRnu_l-kOpXd2I_B3cSvfaTh4lA5g,388
plyer/platforms/macosx/email.py,sha256=YaIL56PkpaJLzA6dThHB6vdm_s3T9yFglvtXuC9ZfFw,1043
plyer/platforms/macosx/filechooser.py,sha256=IzE_y_w_RQpDx6Pgv876cE7V4UFP-IZ_F_4ido-TXHU,3768
plyer/platforms/macosx/keystore.py,sha256=N63RL3b84DVv-6Z-8UL-1CMkjsWEoDt6xh_MP6GElzE,426
plyer/platforms/macosx/libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer/platforms/macosx/libs/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/macosx/libs/__pycache__/osx_motion_sensor.cpython-313.pyc,,
plyer/platforms/macosx/libs/__pycache__/osx_paths.cpython-313.pyc,,
plyer/platforms/macosx/libs/osx_motion_sensor.py,sha256=UYcciBPey-D1-ZFdpftpQvU2SapUKdAvTYrVtmTz6ls,3173
plyer/platforms/macosx/libs/osx_paths.py,sha256=weGrR5cMLUOmzinYEd63DlvsC9qD5P_SDy_bXrODCQA,877
plyer/platforms/macosx/notification.py,sha256=WcsJfveAdTFOabrheCEyDB-DiTCxqzqSTqxfay9PIqk,1407
plyer/platforms/macosx/screenshot.py,sha256=vIlWGKOuyNaa0QVow4wweyN99B-6qIUFVSPPDuvkFNI,687
plyer/platforms/macosx/storagepath.py,sha256=v8_qTaOZ4NmLJmkJ7Hq21cfBMlEa5Wl-c-dVQwnNRzQ,1916
plyer/platforms/macosx/tts.py,sha256=NRe92uzJaDthkr29SBywI-TQ1dNS9ZWCUveOdtoZjNM,605
plyer/platforms/macosx/uniqueid.py,sha256=3ipwwfb_lR_zevalNAdyRjPkdUQU2VCEVmgzY6t_DJA,1071
plyer/platforms/macosx/wifi.py,sha256=O0j3d6OUAcq_0VC2RI14AuziaOZHxvEuM7pLNDlEhkE,5218
plyer/platforms/win/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer/platforms/win/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/win/__pycache__/audio.cpython-313.pyc,,
plyer/platforms/win/__pycache__/battery.cpython-313.pyc,,
plyer/platforms/win/__pycache__/cpu.cpython-313.pyc,,
plyer/platforms/win/__pycache__/devicename.cpython-313.pyc,,
plyer/platforms/win/__pycache__/email.cpython-313.pyc,,
plyer/platforms/win/__pycache__/filechooser.cpython-313.pyc,,
plyer/platforms/win/__pycache__/keystore.cpython-313.pyc,,
plyer/platforms/win/__pycache__/notification.cpython-313.pyc,,
plyer/platforms/win/__pycache__/screenshot.cpython-313.pyc,,
plyer/platforms/win/__pycache__/storagepath.cpython-313.pyc,,
plyer/platforms/win/__pycache__/tts.cpython-313.pyc,,
plyer/platforms/win/__pycache__/uniqueid.cpython-313.pyc,,
plyer/platforms/win/__pycache__/wifi.cpython-313.pyc,,
plyer/platforms/win/audio.py,sha256=KW-cVXfC3s87JHss7m6isprT-Rq9IOayFAq0OYyQpZI,9787
plyer/platforms/win/battery.py,sha256=TPmvKVhQbYbVEWQQGmA-kqN2BpkmWB0IeTZks11L9mE,832
plyer/platforms/win/cpu.py,sha256=w4qVT_zf1VCWe26ZCL75FNOe2u1At99a3e0WamE8Oqo,6646
plyer/platforms/win/devicename.py,sha256=1AyBA_9okWZjck8-WXIcdV31tFAUzC0Ms-SiqYqQIP4,384
plyer/platforms/win/email.py,sha256=u967HQ60--kCxLFNiYUfMaAqFB2McPIjzTGDL10t2sc,1060
plyer/platforms/win/filechooser.py,sha256=SlaRQ4iMtNKeJtMNmIJmnTmLKJZxPa8aqoPYDcOMDWc,5301
plyer/platforms/win/keystore.py,sha256=mGOuoXiUj7-6yI-BmYAF9zPP-vicT8oDSqKkEEI3Ry4,424
plyer/platforms/win/libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
plyer/platforms/win/libs/__pycache__/__init__.cpython-313.pyc,,
plyer/platforms/win/libs/__pycache__/balloontip.cpython-313.pyc,,
plyer/platforms/win/libs/__pycache__/batterystatus.cpython-313.pyc,,
plyer/platforms/win/libs/__pycache__/wifi_defs.cpython-313.pyc,,
plyer/platforms/win/libs/__pycache__/win_api_defs.cpython-313.pyc,,
plyer/platforms/win/libs/balloontip.py,sha256=ivaOhgW3gJRPQJfKRbkQg6QN2ygvzONHa1o1mGlKqnI,6418
plyer/platforms/win/libs/batterystatus.py,sha256=8ciqE3-84r-qGdY6GY8ruo5LHSsObuG213gk6mKeRgk,550
plyer/platforms/win/libs/wifi_defs.py,sha256=BI5qtv4Eak3qz4E6W67eCKTOUEQzrix6aUSCzFhw_Qo,15777
plyer/platforms/win/libs/win_api_defs.py,sha256=MGyElEOAORMjpbQochvMNQ_NktR4ACZm9RVAG-SGLeg,6843
plyer/platforms/win/notification.py,sha256=Y7REWEOma8HTnCq0S-OxKrjUhvohNDVJp5h2vk-pX3s,499
plyer/platforms/win/screenshot.py,sha256=mMzIcvt8Xqd8fMHtKJ4cPnzUK5WsCVoKMVgdmEJ4TWo,3400
plyer/platforms/win/storagepath.py,sha256=AT_UPm0ylaOcBx3AgXCs5B35iXyf3JmbVOJwcuwMvS0,1475
plyer/platforms/win/tts.py,sha256=5EmJf2O2kMYR8szweLRgZoSeqAjfO2-P1XFISd8Ec-I,361
plyer/platforms/win/uniqueid.py,sha256=jZjhEq8d7DsFUrdv2umzLbXbFQS7Jb9HH5i3FHERQWg,785
plyer/platforms/win/wifi.py,sha256=wWkvvzWQ8enHr1w2-Nq2LX77RfWsWytPo2d0D5Y1hMc,4412
plyer/utils.py,sha256=XWXUH2uadC1OqVyx9dFEW3yrJgVhtm9qHY4c4BrfTKs,9554
