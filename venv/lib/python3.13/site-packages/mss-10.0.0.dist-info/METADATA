Metadata-Version: 2.3
Name: mss
Version: 10.0.0
Summary: An ultra fast cross-platform multiple screenshots module in pure python using ctypes.
Project-URL: Homepage, https://github.com/BoboTiG/python-mss
Project-URL: Documentation, https://python-mss.readthedocs.io
Project-URL: Changelog, https://github.com/BoboTiG/python-mss/blob/main/CHANGELOG.md
Project-URL: Source, https://github.com/BoboTiG/python-mss
Project-URL: Sponsor, https://github.com/sponsors/BoboTiG
Project-URL: Tracker, https://github.com/BoboTiG/python-mss/issues
Project-URL: Released Versions, https://github.com/BoboTiG/python-mss/releases
Author-email: <PERSON><PERSON><PERSON> <<EMAIL>>
Maintainer-email: <PERSON><PERSON><PERSON> <<EMAIL>>
License: MIT License
        Copyright (c) 2013-2024, <PERSON><PERSON><PERSON> 'Tiger-222' Schoentgen
        
        Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
Keywords: BitBlt,CGGetActiveDisplayList,CGImageGetBitsPerPixel,EnumDisplayMonitors,XGetImage,XGetWindowAttributes,XRRGetScreenResourcesCurrent,ctypes,monitor,screen,screencapture,screengrab,screenshot
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: MacOS X
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Multimedia :: Graphics :: Capture :: Screen Capture
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.9
Provides-Extra: dev
Requires-Dist: build==1.2.2.post1; extra == 'dev'
Requires-Dist: mypy==1.13.0; extra == 'dev'
Requires-Dist: ruff==0.7.3; extra == 'dev'
Requires-Dist: twine==5.1.1; extra == 'dev'
Provides-Extra: docs
Requires-Dist: sphinx==8.1.3; extra == 'docs'
Provides-Extra: tests
Requires-Dist: numpy==2.1.3; (sys_platform == 'linux' and python_version == '3.13') and extra == 'tests'
Requires-Dist: pillow==11.0.0; (sys_platform == 'linux' and python_version == '3.13') and extra == 'tests'
Requires-Dist: pytest-cov==6.0.0; extra == 'tests'
Requires-Dist: pytest-rerunfailures==14.0.0; extra == 'tests'
Requires-Dist: pytest==8.3.3; extra == 'tests'
Requires-Dist: pyvirtualdisplay==3.0; (sys_platform == 'linux') and extra == 'tests'
Description-Content-Type: text/markdown

# Python MSS

[![PyPI version](https://badge.fury.io/py/mss.svg)](https://badge.fury.io/py/mss)
[![Anaconda version](https://anaconda.org/conda-forge/python-mss/badges/version.svg)](https://anaconda.org/conda-forge/python-mss)
[![Tests workflow](https://github.com/BoboTiG/python-mss/actions/workflows/tests.yml/badge.svg?branch=main)](https://github.com/BoboTiG/python-mss/actions/workflows/tests.yml)
[![Downloads](https://static.pepy.tech/personalized-badge/mss?period=total&units=international_system&left_color=black&right_color=orange&left_text=Downloads)](https://pepy.tech/project/mss)

```python
from mss import mss

# The simplest use, save a screenshot of the 1st monitor
with mss() as sct:
    sct.shot()
```

An ultra-fast cross-platform multiple screenshots module in pure python using ctypes.

- **Python 3.9+**, PEP8 compliant, no dependency, thread-safe;
- very basic, it will grab one screenshot by monitor or a screenshot of all monitors and save it to a PNG file;
- but you can use PIL and benefit from all its formats (or add yours directly);
- integrate well with Numpy and OpenCV;
- it could be easily embedded into games and other software which require fast and platform optimized methods to grab screenshots (like AI, Computer Vision);
- get the [source code on GitHub](https://github.com/BoboTiG/python-mss);
- learn with a [bunch of examples](https://python-mss.readthedocs.io/examples.html);
- you can [report a bug](https://github.com/BoboTiG/python-mss/issues);
- need some help? Use the tag *python-mss* on [Stack Overflow](https://stackoverflow.com/questions/tagged/python-mss);
- and there is a [complete, and beautiful, documentation](https://python-mss.readthedocs.io) :)
- **MSS** stands for Multiple ScreenShots;


## Installation

You can install it with pip:

```shell
python -m pip install -U --user mss
```

Or you can install it with Conda:

```shell
conda install -c conda-forge python-mss
```
