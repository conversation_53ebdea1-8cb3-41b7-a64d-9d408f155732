# Productivity Guard Commands
# Run with: just <command>

# Default: show available commands
default:
    @just --list

# Run demo mode
demo:
    python3 main.py --demo

# Start monitoring
start:
    python3 main.py

# Run with custom interval (seconds)
monitor INTERVAL="5":
    python3 main.py --interval {{INTERVAL}}

# Load tasks from file
load-tasks FILE:
    python3 main.py --task-file {{FILE}}

# Run tests
test:
    python3 test_integration.py

# Install dependencies
install:
    pip install -r requirements.txt