# Productivity Guard Architecture & Interfaces

## System Overview
The Productivity Guard is a modular pipeline system with distinct features that communicate through well-defined interfaces.

## Feature Modules

### 1. Task Management
- **Location**: `task_management/`
- **Responsibility**: Import, track, and manage daily task lists
- **Exports**:
  ```python
  class Task:
      id: str
      title: str
      priority: int
      description: str
      status: str  # "active", "completed", "paused"
  
  class TaskManager:
      def get_current_task() -> Optional[Task]
      def load_tasks(filepath: str) -> List[Task]
      def update_task_status(task_id: str, status: str) -> None
      def get_all_tasks() -> List[Task]
  ```

### 2. Screenshot Capture
- **Location**: `screenshot_capture/`
- **Responsibility**: Capture screenshots at configured intervals
- **Exports**:
  ```python
  class Screenshot:
      timestamp: datetime
      image_path: str
      monitor_id: int
      
  class ScreenshotCapture:
      def capture() -> Screenshot
      def start_capture_loop(interval: int, callback: Callable[[Screenshot], None]) -> None
      def stop_capture_loop() -> None
  ```

### 3. AI Reasoning & Enforcement
- **Location**: `ai_reasoning/`
- **Responsibility**: Analyze screenshots against current task
- **Exports**:
  ```python
  class AIAnalysisResult:
      is_aligned: bool
      confidence: float
      reasoning: str
      suggestions: List[str]
      
  class AIReasoning:
      def analyze_screenshot(screenshot: Screenshot, task: Task) -> AIAnalysisResult
      def should_prompt_user(result: AIAnalysisResult) -> bool
  ```

### 4. Escalation Mode
- **Location**: `escalation/`
- **Responsibility**: Handle user non-compliance
- **Exports**:
  ```python
  class EscalationLevel(Enum):
      NONE = 0
      MILD = 1
      MODERATE = 2
      SEVERE = 3
      
  class EscalationManager:
      def check_escalation_needed(history: List[AIAnalysisResult]) -> EscalationLevel
      def execute_escalation(level: EscalationLevel) -> None
      def send_notification(message: str, urgency: str) -> None
  ```

### 5. Data & Privacy
- **Location**: `data_privacy/`
- **Responsibility**: Store data locally, handle privacy
- **Exports**:
  ```python
  class DataStore:
      def save_screenshot(screenshot: Screenshot) -> None
      def save_analysis(analysis: AIAnalysisResult, screenshot_id: str) -> None
      def get_history(hours: int) -> List[Tuple[Screenshot, AIAnalysisResult]]
      def redact_sensitive_data(image_path: str) -> str
  ```

## Pipeline Flow
```
1. TaskManager.get_current_task() -> current_task
2. ScreenshotCapture.capture() -> screenshot
3. DataStore.save_screenshot(screenshot)
4. AIReasoning.analyze_screenshot(screenshot, current_task) -> result
5. DataStore.save_analysis(result, screenshot.id)
6. If not aligned:
   - EscalationManager.check_escalation_needed(history)
   - EscalationManager.execute_escalation(level)
```

## Shared Types
Located in `shared/types.py`:
- Common data classes
- Configuration types
- Logging utilities

## Configuration
All modules share a common configuration format:
```python
@dataclass
class Config:
    screenshot_interval: int = 5  # seconds
    ai_model: str = "gpt-4"
    escalation_threshold: int = 3  # consecutive misalignments
    data_retention_days: int = 30
```