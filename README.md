# Productivity Guard

AI-driven productivity companion that helps users stay focused on their highest-priority tasks.

## Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Run demo
python main.py --demo

# Run normally
python main.py
```

## Features Implemented

✅ **Architecture & Interfaces** - Modular pipeline system with clear interfaces between features

✅ **Task Management** - Import/track tasks from CSV, JSON, plain text with SQLite persistence

✅ **Screenshot Capture** - Cross-platform screenshot capture using mss library

✅ **AI Reasoning** - Mock implementation analyzing screenshots against current tasks

✅ **Escalation Mode** - Progressive interventions based on compliance history

✅ **Data & Privacy** - Local storage with encryption and sensitive data redaction

✅ **Main Pipeline** - Orchestrates all features in continuous monitoring loop

✅ **Testing** - Unit tests for all modules

## Project Structure

```
.
├── main.py                 # Main pipeline orchestrator
├── ARCHITECTURE.md         # System architecture & interfaces
├── shared/                 # Shared types and utilities
├── task_management/        # Task import & tracking
├── screenshot_capture/     # Screenshot capture module  
├── ai_reasoning/          # AI analysis (mock)
├── escalation/            # Escalation interventions
├── data_privacy/          # Data storage & privacy
└── tests/                 # Test suites
```

## Dependencies

- Python 3.12+
- mss (screenshot capture)
- Pillow (image processing)
- plyer (notifications)
- cryptography (data encryption)

## Running Without Dependencies

For testing without external dependencies:
```bash
python test_integration.py
```

## Configuration

Edit `shared/types.py` to modify default settings:
- Screenshot interval
- AI model selection
- Escalation thresholds
- Data retention period